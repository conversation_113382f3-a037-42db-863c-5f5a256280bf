#!/usr/bin/env python3
"""
Create dataset_samples table for storing individual dataset samples.

This migration creates a new table to store each generated dataset sample
as a separate record, linked to the datagen_event and project.
"""

import psycopg2
import os
from dotenv import load_dotenv

def create_dataset_samples_table():
    """Create the dataset_samples table."""
    
    # Load environment variables
    load_dotenv('.env.local')
    
    # Database connection parameters
    db_params = {
        'host': os.getenv('POSTGRES_HOST', 'localhost'),
        'port': os.getenv('POSTGRES_PORT', '5432'),
        'database': os.getenv('POSTGRES_DB', 'mydb'),
        'user': os.getenv('POSTGRES_USER', 'myuser'),
        'password': os.getenv('POSTGRES_PASSWORD', 'mypassword')
    }
    
    try:
        # Connect to database
        print("🔗 Connecting to database...")
        conn = psycopg2.connect(**db_params)
        cur = conn.cursor()
        
        # Check if table already exists
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'dataset_samples'
            );
        """)
        
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print("Creating dataset_samples table...")
            
            # Create the table
            cur.execute("""
                CREATE TABLE dataset_samples (
                    id UUID PRIMARY KEY,
                    generated_dataset_id UUID REFERENCES generated_datasets(id) ON DELETE CASCADE,
                    datagen_event_id UUID REFERENCES datagen_events(id),
                    project_id UUID REFERENCES projects(id),
                    job_id UUID REFERENCES rainbow_analysis_jobs(id),
                    sample_type VARCHAR(50) NOT NULL,
                    content TEXT NOT NULL,
                    label VARCHAR(255),
                    sample_group INTEGER,
                    sample_index INTEGER,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            
            # Create indexes for better query performance
            cur.execute("""
                CREATE INDEX idx_dataset_samples_generated_dataset_id 
                ON dataset_samples(generated_dataset_id);
            """)
            
            cur.execute("""
                CREATE INDEX idx_dataset_samples_datagen_event_id 
                ON dataset_samples(datagen_event_id);
            """)
            
            cur.execute("""
                CREATE INDEX idx_dataset_samples_project_id 
                ON dataset_samples(project_id);
            """)
            
            cur.execute("""
                CREATE INDEX idx_dataset_samples_job_id 
                ON dataset_samples(job_id);
            """)
            
            cur.execute("""
                CREATE INDEX idx_dataset_samples_label 
                ON dataset_samples(label);
            """)
            
            # Commit the changes
            conn.commit()
            print("✅ dataset_samples table created successfully!")
            print("✅ Indexes created successfully!")
        else:
            print("⚠️ dataset_samples table already exists, skipping creation.")
        
        # Close connections
        cur.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating dataset_samples table: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🚀 Creating dataset_samples table...")
    print("=" * 50)
    
    success = create_dataset_samples_table()
    
    if success:
        print("\n✅ Migration completed successfully!")
    else:
        print("\n❌ Migration failed!")
        exit(1)
