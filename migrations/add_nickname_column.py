#!/usr/bin/env python3
"""
Database migration script to add nickname column to rainbow_analysis_jobs table.

This script adds the nickname column to support Slack notifications.
Run this script if you have an existing database that needs to be updated.

Usage:
    python migrations/add_nickname_column.py
"""

import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_database_url():
    """Get database URL from environment variables."""
    # Default values for local development
    host = os.environ.get('POSTGRES_HOST', 'localhost')
    port = os.environ.get('POSTGRES_PORT', '5432')
    user = os.environ.get('POSTGRES_USER', 'myuser')
    password = os.environ.get('POSTGRES_PASSWORD', 'mypassword')
    database = os.environ.get('POSTGRES_DB', 'mydb')
    
    return f"postgresql://{user}:{password}@{host}:{port}/{database}"


def check_column_exists(engine, table_name, column_name):
    """Check if a column exists in the specified table."""
    query = text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = :table_name 
        AND column_name = :column_name
    """)
    
    with engine.connect() as conn:
        result = conn.execute(query, {
            'table_name': table_name,
            'column_name': column_name
        })
        return result.fetchone() is not None


def add_nickname_column(engine):
    """Add the nickname column to the rainbow_analysis_jobs table."""
    
    # Check if the column already exists
    if check_column_exists(engine, 'rainbow_analysis_jobs', 'nickname'):
        logger.info("✅ Column 'nickname' already exists in 'rainbow_analysis_jobs' table")
        return True
    
    # Add the column
    try:
        with engine.connect() as conn:
            # Start a transaction
            trans = conn.begin()
            try:
                # Add the nickname column
                conn.execute(text("""
                    ALTER TABLE rainbow_analysis_jobs 
                    ADD COLUMN nickname VARCHAR(255)
                """))
                
                # Commit the transaction
                trans.commit()
                logger.info("✅ Successfully added 'nickname' column to 'rainbow_analysis_jobs' table")
                return True
                
            except Exception as e:
                # Rollback on error
                trans.rollback()
                logger.error(f"❌ Failed to add column: {str(e)}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Database connection error: {str(e)}")
        return False


def main():
    """Main migration function."""
    logger.info("🚀 Starting database migration: Adding nickname column")
    
    # Get database URL
    database_url = get_database_url()
    logger.info(f"📊 Connecting to database: {database_url.split('@')[1] if '@' in database_url else 'localhost'}")
    
    try:
        # Create engine
        engine = create_engine(database_url)
        
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("✅ Database connection successful")
        
        # Check if the table exists
        if not check_column_exists(engine, 'rainbow_analysis_jobs', 'id'):
            logger.error("❌ Table 'rainbow_analysis_jobs' does not exist")
            logger.info("💡 Make sure you have run the initial database setup first")
            return False
        
        # Add the nickname column
        success = add_nickname_column(engine)
        
        if success:
            logger.info("🎉 Migration completed successfully!")
            logger.info("💡 You can now use the 'nickname' parameter in API requests for Slack notifications")
            return True
        else:
            logger.error("❌ Migration failed")
            return False
            
    except OperationalError as e:
        logger.error(f"❌ Could not connect to database: {str(e)}")
        logger.info("💡 Make sure PostgreSQL is running and the connection details are correct")
        logger.info("💡 You can set database connection details using environment variables:")
        logger.info("   POSTGRES_HOST, POSTGRES_PORT, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
