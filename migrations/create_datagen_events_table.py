#!/usr/bin/env python3
"""
Migration script to create the datagen_events table.

This table stores form data collected during the dataset generation workflow,
specifically when users transition from step 3 (Expert Collaboration) to step 4.
"""

import os
import sys
import uuid
from datetime import datetime
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import logging
import dotenv

# Load environment variables from .env.local
dotenv.load_dotenv('.env.local')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_datagen_events_table():
    """Create the datagen_events table if it doesn't exist."""
    # Get database connection parameters from environment variables
    db_host = os.getenv('POSTGRES_HOST', 'localhost')
    db_port = os.getenv('POSTGRES_PORT', '5432')
    db_user = os.getenv('POSTGRES_USER', 'myuser')
    db_password = os.getenv('POSTGRES_PASSWORD', 'mypassword')
    db_name = os.getenv('POSTGRES_DB', 'mydb')

    logger.info(f"Connecting to database: {db_host}:{db_port}/{db_name}")

    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        user=db_user,
        password=db_password,
        database=db_name
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    
    # Create a cursor
    cur = conn.cursor()
    
    try:
        # Check if table exists
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public'
                AND table_name = 'datagen_events'
            );
        """)
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print("Creating datagen_events table...")
            
            # Create the table
            cur.execute("""
                CREATE TABLE datagen_events (
                    id UUID PRIMARY KEY,
                    application_description TEXT,
                    domain VARCHAR(100),
                    example_input TEXT,
                    test_types JSONB,
                    complexity INTEGER,
                    coverage INTEGER,
                    dataset_size INTEGER,
                    selected_experts JSONB,
                    uploaded_files JSONB,
                    user_session_id VARCHAR(255),
                    step_completed INTEGER DEFAULT 3,
                    project_id UUID REFERENCES projects(id),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            
            print("✅ datagen_events table created successfully!")
        else:
            print("⚠️ datagen_events table already exists, skipping creation.")
    
    except Exception as e:
        print(f"❌ Error creating datagen_events table: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    create_datagen_events_table()
