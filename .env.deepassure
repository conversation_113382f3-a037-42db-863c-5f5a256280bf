# DeepAssure Azure PostgreSQL Configuration
# Update the credentials below with your actual values

# Database Configuration (Azure PostgreSQL)
POSTGRES_HOST=deepassuredb.postgres.database.azure.com
POSTGRES_PORT=5432
POSTGRES_USER=your_admin_user@deepassuredb
POSTGRES_PASSWORD=your_actual_password
POSTGRES_DB=your_database_name
POSTGRES_SSL=true

# Database Performance Settings
DB_ECHO=false
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Redis Configuration (if using Azure Redis Cache)
REDIS_HOST=localhost
REDIS_PORT=6379

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Logging
LOG_LEVEL=INFO
