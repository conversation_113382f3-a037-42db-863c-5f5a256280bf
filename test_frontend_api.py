#!/usr/bin/env python3
"""
Test script to verify frontend-to-backend API communication.
Tests the API call that the frontend would make through the Next.js proxy.
"""

import requests
import json

def test_frontend_api_call():
    """Test the API call as the frontend would make it."""
    print("🧪 Testing frontend API call through Next.js proxy...")
    
    # This simulates the call the frontend makes to /api/projects
    # which should be proxied to http://localhost:8000/projects
    frontend_url = "http://localhost:3001/api/projects"
    
    project_data = {
        "name": "Frontend Test Project",
        "description": "A test project created via frontend API call",
        "domain": "legal"
    }
    
    try:
        response = requests.post(frontend_url, json=project_data, timeout=10)
        
        if response.status_code == 200:
            project = response.json()
            print(f"✅ Frontend API call successful!")
            print(f"   Project: {project['name']} (ID: {project['id']})")
            return project
        else:
            print(f"❌ Frontend API call failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to frontend server. Make sure it's running on http://localhost:3001")
        return None
    except requests.exceptions.Timeout:
        print("❌ Frontend API call timed out")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None

def test_direct_backend_call():
    """Test direct backend call for comparison."""
    print("\n🧪 Testing direct backend API call...")
    
    backend_url = "http://localhost:8000/projects"
    
    project_data = {
        "name": "Direct Backend Test Project",
        "description": "A test project created via direct backend call",
        "domain": "customer-service"
    }
    
    try:
        response = requests.post(backend_url, json=project_data, timeout=10)
        
        if response.status_code == 200:
            project = response.json()
            print(f"✅ Direct backend call successful!")
            print(f"   Project: {project['name']} (ID: {project['id']})")
            return project
        else:
            print(f"❌ Direct backend call failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Make sure it's running on http://localhost:8000")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None

def main():
    """Run the tests."""
    print("🚀 Testing Frontend-Backend API Communication")
    print("=" * 60)
    
    # Test direct backend call first
    backend_project = test_direct_backend_call()
    
    # Test frontend proxy call
    frontend_project = test_frontend_api_call()
    
    print("\n📊 Test Results:")
    print(f"   Direct Backend: {'✅ Working' if backend_project else '❌ Failed'}")
    print(f"   Frontend Proxy: {'✅ Working' if frontend_project else '❌ Failed'}")
    
    if backend_project and frontend_project:
        print("\n🎉 Both direct backend and frontend proxy are working!")
    elif backend_project and not frontend_project:
        print("\n⚠️  Backend works but frontend proxy has issues.")
        print("   Check Next.js configuration and make sure the rewrite rule is correct.")
    elif not backend_project and not frontend_project:
        print("\n❌ Both backend and frontend have issues.")
        print("   Make sure both servers are running.")
    else:
        print("\n🤔 Unexpected result - frontend works but backend doesn't?")

if __name__ == "__main__":
    main()
