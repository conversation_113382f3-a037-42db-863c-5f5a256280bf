#!/usr/bin/env python3
"""
Test the new individual sample storage functionality.
"""

import uuid as uuid_module
from datetime import datetime, timezone

def test_individual_sample_storage():
    """Test that individual samples are stored correctly in the new DatasetSample table."""
    
    print("🧪 Testing Individual Sample Storage")
    print("=" * 45)
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import Project, DatagenEvent, RainbowAnalysisJob, GeneratedDataset, DatasetSample
        from data_preparation.core.pipeline import Pipeline
        from data_preparation.core.config.base import Config, LLMConfig
        
        # Create test data
        test_project_id = uuid_module.uuid4()
        test_datagen_event_id = uuid_module.uuid4()
        test_job_id = uuid_module.uuid4()
        
        # Sample dataset content with multiple samples
        sample_dataset_content = [
            {
                "samples": [
                    {
                        "type": "content",
                        "content": "**Incident Report**\n**Date:** March 22, 2025\n**Time:** 3:45 PM\n**Location:** Central Park, near the playground\n**Main Type:** Harassment\n**Witness:** <PERSON>\n**Victim:** <PERSON>\n\nOn March 22, 2025, at approximately 3:45 PM, I was at Central Park enjoying the afternoon with my friend <PERSON>.",
                        "label": "Harassment"
                    },
                    {
                        "type": "content",
                        "content": "**Incident Report**\n**Date:** March 23, 2025\n**Time:** 2:15 PM\n**Location:** Downtown Mall\n**Main Type:** Theft\n**Witness:** Alice Brown\n**Victim:** Bob Wilson\n\nI witnessed a theft incident at the downtown mall where someone stole a wallet from Bob Wilson's pocket.",
                        "label": "Theft"
                    },
                    {
                        "type": "content",
                        "content": "**Incident Report**\n**Date:** March 24, 2025\n**Time:** 5:30 PM\n**Location:** University Campus\n**Main Type:** Vandalism\n**Witness:** Carol Davis\n**Victim:** University Property\n\nSomeone spray-painted graffiti on the university library building.",
                        "label": "Vandalism"
                    }
                ]
            }
        ]
        
        with get_db_session() as session:
            print("📝 Step 1: Creating test project...")
            
            # Create test project, datagen event, and job
            test_project = Project(
                id=test_project_id,
                name="Individual Sample Test Project",
                description="Test project for individual sample storage",
                domain="Testing"
            )
            session.add(test_project)
            
            test_datagen_event = DatagenEvent(
                id=test_datagen_event_id,
                application_description="Test application for individual samples",
                domain="Public Safety",
                example_input="Report incidents",
                complexity=5,
                coverage=80,
                dataset_size=100,
                project_id=test_project_id,
                user_session_id="test-session-individual"
            )
            session.add(test_datagen_event)
            
            test_job = RainbowAnalysisJob(
                id=test_job_id,
                prompts=["Test prompt for individual samples"],
                target_llm="test-model",
                num_samples=3,
                num_mutations=1,
                max_iters=1,
                project_id=test_project_id,
                status='running'
            )
            session.add(test_job)
            session.commit()
            
        print(f"✅ Created test data: project={test_project_id}, job={test_job_id}")
        
        print("📝 Step 2: Testing save method directly...")

        # Test the save method directly without creating a full Pipeline instance
        # This simulates what the pipeline save method does
        from rainbowplus.api.models import RainbowAnalysisJob, GeneratedDataset, DatagenEvent, DatasetSample

        with get_db_session() as session:
            # Get the job to find associated project and update status
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == test_job_id).first()
            if not job:
                raise ValueError(f"Job with id {test_job_id} not found in database.")

            # Update job status to completed (keep for backward compatibility)
            job.status = 'completed'
            session.add(job)

            # Find the most recent datagen event for this project/job
            datagen_event = session.query(DatagenEvent)\
                .filter(DatagenEvent.project_id == job.project_id)\
                .order_by(DatagenEvent.created_at.desc())\
                .first()

            # Calculate dataset statistics
            total_samples = 0
            sample_groups = len(sample_dataset_content) if isinstance(sample_dataset_content, list) else 1

            if isinstance(sample_dataset_content, list):
                for group in sample_dataset_content:
                    if isinstance(group, dict) and 'samples' in group:
                        total_samples += len(group['samples'])

            # Create GeneratedDataset record (keep for backward compatibility)
            generated_dataset = GeneratedDataset(
                id=uuid_module.uuid4(),
                datagen_event_id=datagen_event.id if datagen_event else None,
                project_id=job.project_id,
                job_id=job.id,
                dataset_name=f"Generated Dataset {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M')}",
                generation_status='completed',
                dataset_content=sample_dataset_content,
                dataset_size=datagen_event.dataset_size if datagen_event else None,
                complexity=datagen_event.complexity if datagen_event else None,
                coverage=datagen_event.coverage if datagen_event else None,
                total_samples=total_samples,
                sample_groups=sample_groups,
                completed_at=datetime.now(timezone.utc)
            )

            session.add(generated_dataset)
            session.flush()  # Flush to get the generated_dataset.id

            # Now save each individual sample as a separate record
            sample_index = 0
            if isinstance(sample_dataset_content, list):
                for group_index, group in enumerate(sample_dataset_content):
                    if isinstance(group, dict) and 'samples' in group:
                        for sample in group['samples']:
                            if isinstance(sample, dict):
                                # Create individual DatasetSample record
                                dataset_sample = DatasetSample(
                                    id=uuid_module.uuid4(),
                                    generated_dataset_id=generated_dataset.id,
                                    datagen_event_id=datagen_event.id if datagen_event else None,
                                    project_id=job.project_id,
                                    job_id=job.id,
                                    sample_type=sample.get('type', 'content'),
                                    content=sample.get('content', ''),
                                    label=sample.get('label', None),
                                    sample_group=group_index,
                                    sample_index=sample_index
                                )
                                session.add(dataset_sample)
                                sample_index += 1

            session.commit()
            print(f"✅ Saved GeneratedDataset with {sample_index} individual samples")

        print("📝 Step 3: Verifying individual samples were saved...")
        
        with get_db_session() as session:
            # Check GeneratedDataset was created
            generated_dataset = session.query(GeneratedDataset).filter(
                GeneratedDataset.job_id == test_job_id
            ).first()
            
            if generated_dataset:
                print(f"✅ GeneratedDataset created: {generated_dataset.id}")
                print(f"   Status: {generated_dataset.generation_status}")
                print(f"   Total samples: {generated_dataset.total_samples}")
                
                # Check individual DatasetSample records
                dataset_samples = session.query(DatasetSample).filter(
                    DatasetSample.generated_dataset_id == generated_dataset.id
                ).all()
                
                print(f"✅ Found {len(dataset_samples)} individual sample records")
                
                for i, sample in enumerate(dataset_samples):
                    print(f"   Sample {i+1}:")
                    print(f"     ID: {sample.id}")
                    print(f"     Type: {sample.sample_type}")
                    print(f"     Label: {sample.label}")
                    print(f"     Content preview: {sample.content[:100]}...")
                    print(f"     Linked to project: {sample.project_id == test_project_id}")
                    print(f"     Linked to datagen_event: {sample.datagen_event_id == test_datagen_event_id}")
                    print(f"     Linked to job: {sample.job_id == test_job_id}")
                    print(f"     Sample group: {sample.sample_group}")
                    print(f"     Sample index: {sample.sample_index}")
                    print()
                
                # Verify the expected samples are there
                expected_labels = ["Harassment", "Theft", "Vandalism"]
                actual_labels = [sample.label for sample in dataset_samples]
                
                if set(expected_labels) == set(actual_labels):
                    print("✅ All expected sample labels found!")
                else:
                    print(f"❌ Label mismatch. Expected: {expected_labels}, Got: {actual_labels}")
                
                # Clean up test data
                print("📝 Step 4: Cleaning up test data...")
                for sample in dataset_samples:
                    session.delete(sample)
                session.delete(generated_dataset)
                session.delete(test_job)
                session.delete(test_datagen_event)
                session.delete(test_project)
                session.commit()
                print("✅ Test data cleaned up")
                
                return True
            else:
                print("❌ Failed to create GeneratedDataset")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_individual_sample_storage()
    
    if success:
        print("\n🎉 Individual sample storage test passed!")
    else:
        print("\n❌ Individual sample storage test failed!")
        exit(1)
