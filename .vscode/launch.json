{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python Debugger: Current File",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "env": {
        "POSTGRES_USER": "myuser",
        "POSTGRES_PASSWORD": "mypassword",
        "POSTGRES_DB": "mydb",
        "POSTGRES_HOST": "**************",
        // "POSTGRES_HOST": "deepassuredb.postgres.database.azure.com",
        // "POSTGRES_USER": "da",
        // "POSTGRES_PASSWORD": "deepassure2025@",
        // "POSTGRES_SSL": "true",
        // "POSTGRES_DB": "rainbowplus",
        // "POSTGRES_DB": "mydb",
        "POSTGRES_PORT": "5432",
        // "REDIS_HOST": "**************",
        // "REDIS_PORT": "6379",
        "REDIS_HOST": "deepassuresg.redis.cache.windows.net",
        "REDIS_PORT": "6380",
        "REDIS_KEY": "0A7RllqmDlVKuFO1w0jjfUMdeEUgAjHYIAzCaIuEPvI=",
        "REDIS_TLS": "true",
        "SLACK_WEBHOOK_URL": "*********************************************************************************"
      },
    },
    {
      "name": "Python: FastAPI - Module Debug",
      "type": "python",
      "request": "launch",
      "module": "rainbowplus.api.app",
      "cwd": "${workspaceFolder}",
      "env": {
        // "POSTGRES_USER": "myuser",
        // "POSTGRES_PASSWORD": "mypassword",
        // "POSTGRES_DB": "mydb",
        // "POSTGRES_HOST": "**************",
        "POSTGRES_HOST": "deepassuredb.postgres.database.azure.com",
        "POSTGRES_USER": "da",
        "POSTGRES_PASSWORD": "deepassure2025@",
        "POSTGRES_SSL": "true",
        // "POSTGRES_DB": "rainbowplus",
        "POSTGRES_DB": "mydb",
        "POSTGRES_PORT": "5432",
        // "REDIS_HOST": "**************",
        // "REDIS_PORT": "6379",
        "REDIS_HOST": "deepassuresg.redis.cache.windows.net",
        "REDIS_PORT": "6380",
        "REDIS_KEY": "0A7RllqmDlVKuFO1w0jjfUMdeEUgAjHYIAzCaIuEPvI=",
        "REDIS_TLS": "true",
        "SLACK_WEBHOOK_URL": "*********************************************************************************"
      },
      "args": [],
      "console": "integratedTerminal",
      "justMyCode": true
    }
  ]
}

