#!/usr/bin/env python3
"""
Example script demonstrating the new configuration system with dependency injection.
Shows how to easily switch between YAML and database configuration sources.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rainbowplus.configs.run import ConfigurationManager, ConfigurationLoader


def example_yaml_usage():
    """Example using YAML configuration (backward compatible)."""
    print("🔧 Example 1: YAML Configuration (Backward Compatible)")
    print("=" * 60)
    
    try:
        # Method 1: Using the old ConfigurationLoader (still works)
        from rainbowplus.configs.run import ConfigurationLoader as OldLoader
        config = OldLoader.load("./configs/base.yml")
        print(f"✅ Loaded config using old method: {config.target_llm.model_kwargs.get('model', 'N/A')}")
        
        # Method 2: Using new ConfigurationManager with YAML source
        manager = ConfigurationManager.create_yaml_manager()
        config = manager.load("./configs/base.yml")
        print(f"✅ Loaded config using new YAML manager: {config.target_llm.model_kwargs.get('model', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error loading YAML config: {e}")
    
    print()


def example_database_usage():
    """Example using database configuration."""
    print("🗄️  Example 2: Database Configuration")
    print("=" * 60)
    
    try:
        # Create database manager
        manager = ConfigurationManager.create_database_manager()
        
        # Try to load a template (this will fail if no templates exist)
        try:
            config = manager.load("default_template")
            print(f"✅ Loaded config from database: {config.target_llm.model_kwargs.get('model', 'N/A')}")
        except ValueError as e:
            print(f"ℹ️  No database template found: {e}")
            print("   Run the migration script first to create templates:")
            print("   python -m rainbowplus.configs.migrate_database --import-yaml ./configs/base.yml --template-name default_template")
        
    except Exception as e:
        print(f"❌ Error with database config: {e}")
    
    print()


def example_custom_usage():
    """Example showing how to create custom configuration sources."""
    print("🔧 Example 3: Custom Configuration Source")
    print("=" * 60)
    
    try:
        from rainbowplus.configs.loaders import ConfigurationSource, ConfigurationLoader
        from rainbowplus.configs.base import LLMConfig
        
        class MockConfigurationSource(ConfigurationSource):
            """Mock configuration source for demonstration."""
            
            def load_config_data(self, identifier: str):
                """Return mock configuration data."""
                return {
                    'archive': {'path': ['./mock/categories.txt'], 'descriptor': ['Mock Category']},
                    'sample_prompts': './mock/prompts.json',
                    'target_llm': {
                        'type_': 'azure_openai',
                        'api_key': 'mock-key',
                        'azure_endpoint': 'https://mock-resource.openai.azure.com/',
                        'api_version': '2024-02-15-preview',
                        'model_kwargs': {'model': 'gpt-3.5-turbo'},
                        'sampling_params': {'temperature': 0.7}
                    },
                    'fitness_llm': {
                        'type_': 'azure_openai',
                        'api_key': 'mock-key',
                        'azure_endpoint': 'https://mock-resource.openai.azure.com/',
                        'api_version': '2024-02-15-preview',
                        'model_kwargs': {'model': 'gpt-4'},
                        'sampling_params': {'temperature': 0.5}
                    }
                }
        
        # Create custom loader
        custom_source = MockConfigurationSource()
        custom_loader = ConfigurationLoader(custom_source)
        manager = ConfigurationManager(custom_loader)
        
        # Load configuration
        config = manager.load("mock_identifier")
        print(f"✅ Loaded mock config: {config.target_llm.model_kwargs.get('model', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error with custom config: {e}")
    
    print()


def example_service_integration():
    """Example showing how to integrate with existing services."""
    print("🔗 Example 4: Service Integration")
    print("=" * 60)
    
    try:
        # Simulate how you would modify existing code
        def load_config_with_dependency_injection(use_database=False, template_name="default_template"):
            """
            Example function showing how to modify existing code to support both sources.
            
            Args:
                use_database: If True, use database source; otherwise use YAML
                template_name: Template name for database source
            """
            if use_database:
                manager = ConfigurationManager.create_database_manager()
                identifier = template_name
                source_type = "database"
            else:
                manager = ConfigurationManager.create_yaml_manager()
                identifier = "./configs/base.yml"
                source_type = "YAML"
            
            try:
                config = manager.load(identifier)
                print(f"✅ Loaded config from {source_type}: {config.target_llm.model_kwargs.get('model', 'N/A')}")
                return config
            except Exception as e:
                print(f"❌ Failed to load config from {source_type}: {e}")
                return None
        
        # Test both methods
        print("Testing YAML source:")
        load_config_with_dependency_injection(use_database=False)
        
        print("Testing database source:")
        load_config_with_dependency_injection(use_database=True)
        
    except Exception as e:
        print(f"❌ Error in service integration example: {e}")
    
    print()


def main():
    """Run all examples."""
    print("🌈 RainbowPlus Configuration System Examples")
    print("=" * 60)
    print("This script demonstrates the new dependency injection-based")
    print("configuration system that supports both YAML and database sources.")
    print()
    
    # Run examples
    example_yaml_usage()
    example_database_usage()
    example_custom_usage()
    example_service_integration()
    
    print("🎉 Examples completed!")
    print()
    print("💡 Quick Start:")
    print("   # Use YAML (backward compatible)")
    print("   manager = ConfigurationManager.create_yaml_manager()")
    print("   config = manager.load('./configs/base.yml')")
    print()
    print("   # Use database")
    print("   manager = ConfigurationManager.create_database_manager()")
    print("   config = manager.load('template_name')")


if __name__ == "__main__":
    main()
