import unittest
from unittest.mock import MagicMock
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../data-preparation')))

from data_preparation.core.pipeline import Pipeline
from rainbowplus.api.models import RainbowAnalysisJob
from rainbowplus.api.database import get_db_session
import uuid

class TestPipelineDatabaseOutput(unittest.TestCase):
    def setUp(self):
        # Setup a test config mock
        class ConfigMock:
            def __init__(self):
                self.llm = MagicMock()
                self.llm.sampling_params = {}
                self.task = "Content-Generation"
                self.description = "test_description.txt"
                self.examples = []
                self.documents = []
                self._file_path = ""
                self.api_key = None
                self.base_url = None

        self.config = ConfigMock()

        # Setup a mock LLM generate_format method to return a mock sample with model_dump
        mock_sample = MagicMock()
        mock_sample.model_dump.return_value = {"mock": "data"}
        self.mock_samples = [mock_sample]

        self.config.llm = MagicMock()
        self.config.llm.sampling_params = {}
        self.config.llm.api_key = None
        self.config.llm.base_url = None
        # Patch the description path to a dummy existing file to avoid FileNotFoundError
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False) as tmpfile:
            self.config.description = tmpfile.name
            self.pipeline = Pipeline(self.config)

        # Patch the llm.generate_format method
        self.pipeline.llm.generate_format = MagicMock(return_value=self.mock_samples)

    def test_generate_writes_to_database(self):
        # Create a test job in the database
        with get_db_session() as session:
            job = RainbowAnalysisJob(
                id=uuid.uuid4(),
                prompts=["test prompt"],
                target_llm="test-llm",
                num_samples=1,
                num_mutations=1,
                max_iters=1,
                status="pending"
            )
            session.add(job)
            session.flush()
            job_id = job.id

        # Call generate with job_id to write to database
        result = self.pipeline.generate(job_id=job_id)

        # Verify the result matches the mock sample dump
        self.assertEqual(result, [sample.model_dump() for sample in self.mock_samples])

        # Verify the database record is updated
        with get_db_session() as session:
            updated_job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
            self.assertIsNotNone(updated_job)
            self.assertEqual(updated_job.status, "completed")
            self.assertEqual(updated_job.results, [sample.model_dump() for sample in self.mock_samples])

    def test_generate_fallback_to_file(self):
        # Test that output_path saves to file if no job_id provided
        import tempfile
        import os

        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, "output.json")
            result = self.pipeline.generate(output_path=output_path)

            # Check file exists
            self.assertTrue(os.path.exists(output_path))

            # Check file content matches result
            with open(output_path, "r") as f:
                import json
                file_content = json.load(f)
                self.assertEqual(file_content, result)

if __name__ == "__main__":
    unittest.main()