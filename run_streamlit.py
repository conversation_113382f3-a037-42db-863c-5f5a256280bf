#!/usr/bin/env python3
"""
Launcher script for RainbowPlus Streamlit interface.
Handles dependency installation and app startup.

Usage:
    python run_streamlit.py
    python run_streamlit.py --port 8502
    python run_streamlit.py --install-deps
"""

import subprocess
import sys
import os
import argparse
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'streamlit',
        'requests', 
        'pandas',
        'plotly'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages


def install_dependencies():
    """Install required dependencies."""
    requirements_file = Path("streamlit_requirements.txt")
    
    if not requirements_file.exists():
        print("❌ streamlit_requirements.txt not found!")
        return False
    
    print("📦 Installing Streamlit dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def run_streamlit(port=8501):
    """Run the Streamlit application."""
    app_file = Path("streamlit_app.py")
    
    if not app_file.exists():
        print("❌ streamlit_app.py not found!")
        return False
    
    print(f"🚀 Starting RainbowPlus Streamlit interface on port {port}...")
    print(f"   Open your browser to: http://localhost:{port}")
    print("   Press Ctrl+C to stop the server")
    print()
    
    try:
        subprocess.run([
            "streamlit", "run", str(app_file),
            "--server.port", str(port),
            "--server.address", "0.0.0.0",
            "--browser.gatherUsageStats", "false",
            "--theme.base", "light",
            "--theme.backgroundColor", "#ffffff",
            "--theme.textColor", "#000000"
        ])
        return True
    except KeyboardInterrupt:
        print("\n👋 Streamlit server stopped")
        return True
    except FileNotFoundError:
        print("❌ Streamlit not found! Please install it first:")
        print("   pip install streamlit")
        return False
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")
        return False


def main():
    """Main launcher function."""
    parser = argparse.ArgumentParser(description="Launch RainbowPlus Streamlit Interface")
    parser.add_argument("--port", type=int, default=8501, help="Port to run Streamlit on")
    parser.add_argument("--install-deps", action="store_true", help="Install dependencies first")
    parser.add_argument("--check-deps", action="store_true", help="Check dependencies only")
    
    args = parser.parse_args()
    
    print("🌈 RainbowPlus Streamlit Launcher")
    print("=" * 40)
    
    # Check dependencies
    missing_deps = check_dependencies()
    
    if args.check_deps:
        if missing_deps:
            print(f"❌ Missing dependencies: {', '.join(missing_deps)}")
            print("   Run with --install-deps to install them")
        else:
            print("✅ All dependencies are installed!")
        return
    
    # Install dependencies if requested or missing
    if args.install_deps or missing_deps:
        if missing_deps:
            print(f"⚠️  Missing dependencies: {', '.join(missing_deps)}")
        
        if not install_dependencies():
            print("❌ Failed to install dependencies")
            return
        
        # Re-check after installation
        missing_deps = check_dependencies()
        if missing_deps:
            print(f"❌ Still missing dependencies: {', '.join(missing_deps)}")
            return
    
    # Run Streamlit
    success = run_streamlit(args.port)
    
    if not success:
        print("\n💡 Troubleshooting tips:")
        print("   1. Make sure all dependencies are installed: python run_streamlit.py --install-deps")
        print("   2. Check if the port is already in use")
        print("   3. Try a different port: python run_streamlit.py --port 8502")
        print("   4. Check the Streamlit documentation: https://docs.streamlit.io/")


if __name__ == "__main__":
    main()
