#!/usr/bin/env python3
"""
Test script to verify project selection functionality in the UI.
"""

import requests
import json
import time

# API endpoints
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3002"

def test_projects_api():
    """Test that the projects API is working."""
    print("🧪 Testing projects API...")
    
    response = requests.get(f"{BASE_URL}/projects")
    
    if response.status_code == 200:
        data = response.json()
        projects = data.get("projects", [])
        print(f"✅ Projects API working: {len(projects)} projects found")
        
        # Show first few projects
        for i, project in enumerate(projects[:3]):
            print(f"   {i+1}. {project['name']} ({project['domain']}) - {project['id']}")
        
        return projects
    else:
        print(f"❌ Projects API failed: {response.status_code} - {response.text}")
        return []

def test_frontend_api_proxy():
    """Test that the frontend API proxy is working."""
    print("\n🧪 Testing frontend API proxy...")
    
    try:
        response = requests.get(f"{FRONTEND_URL}/api/projects", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            projects = data.get("projects", [])
            print(f"✅ Frontend API proxy working: {len(projects)} projects found")
            return True
        else:
            print(f"❌ Frontend API proxy failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend API proxy error: {e}")
        return False

def test_dataset_generation_with_project():
    """Test dataset generation with project selection."""
    print("\n🧪 Testing dataset generation with project selection...")
    
    # First, create a test project
    project_data = {
        "name": "UI Test Project",
        "description": "Test project for UI project selection functionality",
        "domain": "legal"
    }
    
    response = requests.post(f"{BASE_URL}/projects", json=project_data)
    
    if response.status_code == 200:
        project = response.json()
        project_id = project["id"]
        print(f"✅ Test project created: {project['name']} - {project_id}")
        
        # Test dataset generation with this project
        dataset_data = {
            "description": "Test dataset generation with UI project selection",
            "examples": ["Test example for UI project selection"],
            "task": "Content",
            "project_id": project_id,
            "nickname": "UI Test"
        }
        
        response = requests.post(f"{BASE_URL}/datasets/generate", json=dataset_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Dataset generation started: {result['job_id']}")
            
            # Verify the project association
            response = requests.get(f"{BASE_URL}/datagen/events?project_id={project_id}")
            if response.status_code == 200:
                events = response.json().get("events", [])
                print(f"✅ Project association verified: {len(events)} events found for project")
            
            return True
        else:
            print(f"❌ Dataset generation failed: {response.status_code} - {response.text}")
            return False
    else:
        print(f"❌ Test project creation failed: {response.status_code} - {response.text}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Project Selection UI Functionality")
    print("=" * 50)
    
    # Test backend API
    projects = test_projects_api()
    
    # Test frontend proxy
    frontend_working = test_frontend_api_proxy()
    
    # Test dataset generation with project
    dataset_working = test_dataset_generation_with_project()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Backend API: {'✅ Working' if projects else '❌ Failed'}")
    print(f"   Frontend Proxy: {'✅ Working' if frontend_working else '❌ Failed'}")
    print(f"   Dataset Generation: {'✅ Working' if dataset_working else '❌ Failed'}")
    
    if projects and frontend_working and dataset_working:
        print("\n🎉 All tests passed! Project selection UI should be working correctly.")
        print(f"   Frontend URL: {FRONTEND_URL}")
        print("   Navigate to 'Generate Dataset' section to test the UI.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
