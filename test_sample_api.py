#!/usr/bin/env python3
"""
Test the new dataset sample API endpoints.
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_sample_api():
    """Test the dataset sample API endpoints."""
    
    print("🧪 Testing Dataset Sample API Endpoints")
    print("=" * 50)
    
    try:
        # Test 1: List all dataset samples
        print("📝 Test 1: List all dataset samples...")
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=5")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data['total_count']} total samples")
            print(f"   Returned {len(data['samples'])} samples in this page")
            
            if data['samples']:
                first_sample = data['samples'][0]
                print(f"   First sample ID: {first_sample['id']}")
                print(f"   First sample label: {first_sample['label']}")
                print(f"   First sample content preview: {first_sample['content'][:100]}...")
        else:
            print(f"❌ Failed with status {response.status_code}: {response.text}")
        
        # Test 2: Filter by label
        print("\n📝 Test 2: Filter samples by label 'Property'...")
        response = requests.get(f"{BASE_URL}/datasets/samples?label=Property&limit=3")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data['total_count']} Property samples")
            print(f"   Returned {len(data['samples'])} samples in this page")
            
            for i, sample in enumerate(data['samples']):
                print(f"   Sample {i+1}: {sample['label']} - {sample['content'][:50]}...")
        else:
            print(f"❌ Failed with status {response.status_code}: {response.text}")
        
        # Test 3: Get a specific sample
        if 'samples' in locals() and data['samples']:
            sample_id = data['samples'][0]['id']
            print(f"\n📝 Test 3: Get specific sample {sample_id}...")
            response = requests.get(f"{BASE_URL}/datasets/samples/{sample_id}")
            
            if response.status_code == 200:
                sample = response.json()
                print(f"✅ Success! Retrieved sample:")
                print(f"   ID: {sample['id']}")
                print(f"   Type: {sample['sample_type']}")
                print(f"   Label: {sample['label']}")
                print(f"   Content length: {len(sample['content'])} characters")
                print(f"   Sample group: {sample['sample_group']}")
                print(f"   Sample index: {sample['sample_index']}")
            else:
                print(f"❌ Failed with status {response.status_code}: {response.text}")
        
        # Test 4: Filter by generated_dataset_id
        print("\n📝 Test 4: Filter by generated_dataset_id...")
        # First get a generated_dataset_id from the samples
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=1")
        if response.status_code == 200:
            data = response.json()
            if data['samples']:
                generated_dataset_id = data['samples'][0]['generated_dataset_id']
                print(f"   Using generated_dataset_id: {generated_dataset_id}")
                
                response = requests.get(f"{BASE_URL}/datasets/samples?generated_dataset_id={generated_dataset_id}&limit=5")
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Success! Found {data['total_count']} samples for this dataset")
                    print(f"   Returned {len(data['samples'])} samples in this page")
                else:
                    print(f"❌ Failed with status {response.status_code}: {response.text}")
        
        print("\n🎉 All API tests completed!")
        
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sample_api()
