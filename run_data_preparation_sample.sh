#!/bin/bash

# This script runs the data-preparation pipeline with sample config and output paths

# Set default config and output paths
CONFIG_PATH="data-preparation/recipes/base.yml"
OUTPUT_PATH="data-preparation/outputs/samples.json"

# Create output directory if it doesn't exist
mkdir -p "$(dirname "$OUTPUT_PATH")"

# Run the pipeline
export PYTHONPATH=data-preparation
python3 data-preparation/core/pipeline.py --config "$CONFIG_PATH" --output "$OUTPUT_PATH"

echo "Data preparation completed. Output saved to $OUTPUT_PATH"