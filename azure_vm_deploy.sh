#!/bin/bash
# Script to create and configure an Azure VM for RainbowPlus

# Set variables
RESOURCE_GROUP="rainbowplus-rg"
LOCATION="southeastasia"
VM_NAME="rainbowplus-vm"
VM_SIZE="Standard_B2s"
ADMIN_USERNAME="azureuser"
NSG_NAME="rainbowplus-nsg"

# Create resource group if it doesn't exist
az group create --name $RESOURCE_GROUP --location "$LOCATION"

# Create network security group
az network nsg create --resource-group $RESOURCE_GROUP --name $NSG_NAME

# Add rules to allow SSH and HTTP/API traffic
az network nsg rule create --resource-group $RESOURCE_GROUP --nsg-name $NSG_NAME \
  --name SSH --priority 1000 --protocol Tcp --destination-port-range 22 --access Allow
az network nsg rule create --resource-group $RESOURCE_GROUP --nsg-name $NSG_NAME \
  --name HTTP --priority 1010 --protocol Tcp --destination-port-range 8000 --access Allow

# Create VM with public IP
az vm create \
  --resource-group $RESOURCE_GROUP \
  --name $VM_NAME \
  --image UbuntuLTS \
  --size $VM_SIZE \
  --admin-username $ADMIN_USERNAME \
  --generate-ssh-keys \
  --nsg $NSG_NAME \
  --public-ip-sku Standard

# Get the public IP address
PUBLIC_IP=$(az vm show --resource-group $RESOURCE_GROUP --name $VM_NAME --show-details \
  --query publicIps -o tsv)

echo "VM created with IP: $PUBLIC_IP"

# Optional: Run setup commands on the VM
echo "You can now SSH to your VM with: ssh $ADMIN_USERNAME@$PUBLIC_IP"
echo "Then run the following commands to set up your application:"
echo "1. sudo apt-get update && sudo apt-get install -y docker.io docker-compose python3-pip"
echo "2. git clone <your-repo-url>"
echo "3. cd <your-repo-directory>"
echo "4. Update .env files with your Azure PostgreSQL credentials"
echo "5. docker-compose up -d"