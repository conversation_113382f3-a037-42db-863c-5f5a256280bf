#!/usr/bin/env python3
"""
Test dataset generation with a working API key to see if the new GeneratedDataset table gets populated.
"""

import requests
import json
import time

# API endpoints
BASE_URL = "http://localhost:8000"
DATAGEN_EVENT_URL = f"{BASE_URL}/datagen/events"
DATASET_GENERATION_URL = f"{BASE_URL}/datasets/generate"

def test_dataset_generation_with_api_key():
    """Test dataset generation with the API key from base.yml."""
    
    print("🧪 Testing Dataset Generation with Working API Key")
    print("=" * 60)
    
    # Step 1: Create a datagen event
    print("📝 Step 1: Creating datagen event...")
    
    datagen_event_data = {
        "application_description": "Test application for incident reporting with working API",
        "domain": "Public Safety",
        "example_input": "Report suspicious activity in the park",
        "test_types": [
            {"id": "harassment", "label": "Harassment", "checked": True},
            {"id": "cheating", "label": "Cheating", "checked": True}
        ],
        "complexity": 5,
        "coverage": 70,
        "dataset_size": 10,  # Small size for testing
        "selected_experts": ["safety_expert", "legal_expert"],
        "uploaded_files": ["test_file.txt"],
        "user_session_id": "test-session-debug-123",
        "project_id": None
    }
    
    try:
        event_response = requests.post(DATAGEN_EVENT_URL, json=datagen_event_data)
        event_response.raise_for_status()
        event_result = event_response.json()
        print(f"✅ Datagen event created: {event_result}")
        event_id = event_result.get('event_id')
    except Exception as e:
        print(f"❌ Failed to create datagen event: {e}")
        return False
    
    # Step 2: Generate dataset with API key from base.yml
    print("\n🚀 Step 2: Generating dataset with working API key...")
    
    # Use the API key from base.yml
    api_key = "********************************************************************************************************************************************************************"
    
    dataset_generation_data = {
        "description": "Test application for incident reporting with working API",
        "examples": ["Report suspicious activity in the park"],
        "task": "Content",
        "llm": {
            "provider": "openai",
            "api_key": api_key,
            "model_kwargs": {
                "model": "gpt-4o-mini"
            },
            "sampling_params": {
                "temperature": 0.7,
                "max_tokens": 1000,
                "top_p": 0.9,
                "n": 2  # Generate 2 samples for testing
            }
        },
        "nickname": "debug-test-user",
        "project_id": None
    }
    
    try:
        dataset_response = requests.post(DATASET_GENERATION_URL, json=dataset_generation_data)
        dataset_response.raise_for_status()
        dataset_result = dataset_response.json()
        print(f"✅ Dataset generation started: {dataset_result}")
        job_id = dataset_result.get('job_id')
    except Exception as e:
        print(f"❌ Failed to start dataset generation: {e}")
        return False
    
    # Step 3: Wait for generation to complete
    print(f"\n⏳ Step 3: Waiting for dataset generation to complete (job_id: {job_id})...")
    print("   (Check server logs for debug messages)")
    
    # Wait longer for the API call to complete
    time.sleep(15)
    
    # Step 4: Check database for results
    print("\n🔍 Step 4: Checking database for results...")
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import GeneratedDataset, RainbowAnalysisJob, DatagenEvent
        
        with get_db_session() as session:
            # Check job status
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
            if job:
                print(f"📊 Job status: {job.status}")
                print(f"📊 Job created: {job.created_at}")
                print(f"📊 Job has results: {bool(job.results)}")
                if job.results:
                    print(f"📊 Results type: {type(job.results)}")
                    print(f"📊 Results size: {len(str(job.results))} chars")
                
                # Check for GeneratedDataset
                generated_dataset = session.query(GeneratedDataset).filter(
                    GeneratedDataset.job_id == job_id
                ).first()
                
                if generated_dataset:
                    print(f"🎉 SUCCESS: GeneratedDataset found!")
                    print(f"   ID: {generated_dataset.id}")
                    print(f"   Status: {generated_dataset.generation_status}")
                    print(f"   Dataset name: {generated_dataset.dataset_name}")
                    print(f"   Total samples: {generated_dataset.total_samples}")
                    print(f"   Sample groups: {generated_dataset.sample_groups}")
                    print(f"   Datagen event ID: {generated_dataset.datagen_event_id}")
                    print(f"   Created: {generated_dataset.created_at}")
                    print(f"   Completed: {generated_dataset.completed_at}")
                    
                    if generated_dataset.dataset_content:
                        print(f"   Dataset content size: {len(str(generated_dataset.dataset_content))} chars")
                        print(f"   Content preview: {str(generated_dataset.dataset_content)[:200]}...")
                    
                    return True
                else:
                    print("❌ No GeneratedDataset found for this job")
                    print("💡 This suggests the new save method is not being called")
                    return False
            else:
                print(f"❌ Job {job_id} not found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 Dataset Generation Debug Test")
    print("Testing with working API key to see debug logs")
    print()
    
    success = test_dataset_generation_with_api_key()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test successful!")
        print("✅ GeneratedDataset table is being populated correctly")
    else:
        print("❌ Test failed!")
        print("💡 Check server logs for debug messages")
        print("💡 The issue might be in the pipeline code path")
    
    return success

if __name__ == "__main__":
    main()
