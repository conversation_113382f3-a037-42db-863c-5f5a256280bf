#!/usr/bin/env python3
"""
Direct database test for GeneratedDataset table functionality.
This test bypasses the LLM API calls and directly tests the database operations.
"""

import uuid
from datetime import datetime, timezone

def test_generated_dataset_table():
    """Test the GeneratedDataset table operations directly."""
    
    print("🧪 Testing GeneratedDataset Table Operations")
    print("=" * 50)
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import GeneratedDataset, DatagenEvent, Project, RainbowAnalysisJob
        
        # Test data
        test_project_id = uuid.uuid4()
        test_datagen_event_id = uuid.uuid4()
        test_job_id = uuid.uuid4()
        
        # Sample dataset content (similar to samples.json structure)
        sample_dataset_content = [
            {
                "samples": [
                    {
                        "type": "content",
                        "content": "Test incident report about harassment in the park",
                        "label": "Harassment"
                    },
                    {
                        "type": "content", 
                        "content": "Test incident report about cheating at the store",
                        "label": "Cheating"
                    }
                ]
            }
        ]
        
        with get_db_session() as session:
            print("📝 Step 1: Creating test project...")
            
            # Create a test project
            test_project = Project(
                id=test_project_id,
                name="Test Project for Dataset Generation",
                description="Test project to verify GeneratedDataset functionality",
                domain="Testing"
            )
            session.add(test_project)
            session.flush()
            print(f"✅ Created test project: {test_project_id}")
            
            print("📝 Step 2: Creating test datagen event...")
            
            # Create a test datagen event
            test_datagen_event = DatagenEvent(
                id=test_datagen_event_id,
                application_description="Test application for incident reporting",
                domain="Public Safety",
                example_input="Report suspicious activity",
                complexity=5,
                coverage=70,
                dataset_size=100,
                project_id=test_project_id,
                user_session_id="test-session-123"
            )
            session.add(test_datagen_event)
            session.flush()
            print(f"✅ Created test datagen event: {test_datagen_event_id}")
            
            print("📝 Step 3: Creating test job...")
            
            # Create a test job
            test_job = RainbowAnalysisJob(
                id=test_job_id,
                prompts=["Test prompt"],
                target_llm="gpt-4o-mini",
                num_samples=5,
                num_mutations=3,
                max_iters=1,
                project_id=test_project_id,
                status='completed'
            )
            session.add(test_job)
            session.flush()
            print(f"✅ Created test job: {test_job_id}")
            
            print("📝 Step 4: Creating GeneratedDataset...")
            
            # Create a GeneratedDataset
            generated_dataset = GeneratedDataset(
                id=uuid.uuid4(),
                datagen_event_id=test_datagen_event_id,
                project_id=test_project_id,
                job_id=test_job_id,
                dataset_name="Test Generated Dataset",
                generation_status='completed',
                dataset_content=sample_dataset_content,
                dataset_size=100,
                complexity=5,
                coverage=70,
                total_samples=2,
                sample_groups=1,
                completed_at=datetime.now(timezone.utc)
            )
            session.add(generated_dataset)
            session.commit()
            
            generated_dataset_id = generated_dataset.id
            print(f"✅ Created GeneratedDataset: {generated_dataset_id}")
            
            print("📝 Step 5: Verifying relationships...")
            
            # Test querying the GeneratedDataset with relationships
            retrieved_dataset = session.query(GeneratedDataset).filter(
                GeneratedDataset.id == generated_dataset_id
            ).first()
            
            if retrieved_dataset:
                print(f"✅ Retrieved GeneratedDataset: {retrieved_dataset.id}")
                print(f"   Dataset name: {retrieved_dataset.dataset_name}")
                print(f"   Status: {retrieved_dataset.generation_status}")
                print(f"   Total samples: {retrieved_dataset.total_samples}")
                print(f"   Sample groups: {retrieved_dataset.sample_groups}")
                
                # Test relationships
                if retrieved_dataset.project:
                    print(f"   ✅ Project relationship: {retrieved_dataset.project.name}")
                else:
                    print("   ❌ Project relationship not working")
                    
                if retrieved_dataset.datagen_event:
                    print(f"   ✅ DatagenEvent relationship: {retrieved_dataset.datagen_event.application_description}")
                else:
                    print("   ❌ DatagenEvent relationship not working")
                    
                if retrieved_dataset.job:
                    print(f"   ✅ Job relationship: {retrieved_dataset.job.status}")
                else:
                    print("   ❌ Job relationship not working")
                
                # Test dataset content
                if retrieved_dataset.dataset_content:
                    print(f"   ✅ Dataset content preserved: {len(str(retrieved_dataset.dataset_content))} chars")
                    print(f"   Content preview: {str(retrieved_dataset.dataset_content)[:100]}...")
                else:
                    print("   ❌ Dataset content not preserved")
                
                print("📝 Step 6: Testing queries by relationships...")
                
                # Test querying by project
                datasets_by_project = session.query(GeneratedDataset).filter(
                    GeneratedDataset.project_id == test_project_id
                ).all()
                print(f"   ✅ Found {len(datasets_by_project)} datasets for project")
                
                # Test querying by datagen event
                datasets_by_event = session.query(GeneratedDataset).filter(
                    GeneratedDataset.datagen_event_id == test_datagen_event_id
                ).all()
                print(f"   ✅ Found {len(datasets_by_event)} datasets for datagen event")
                
                # Test querying by job
                datasets_by_job = session.query(GeneratedDataset).filter(
                    GeneratedDataset.job_id == test_job_id
                ).all()
                print(f"   ✅ Found {len(datasets_by_job)} datasets for job")
                
                print("📝 Step 7: Cleaning up test data...")
                
                # Clean up test data
                session.delete(retrieved_dataset)
                session.delete(test_job)
                session.delete(test_datagen_event)
                session.delete(test_project)
                session.commit()
                print("✅ Test data cleaned up")
                
                return True
            else:
                print("❌ Failed to retrieve GeneratedDataset")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_save_method():
    """Test the pipeline's _save_generated_dataset_to_db method."""
    
    print("\n🧪 Testing Pipeline Save Method")
    print("=" * 40)
    
    try:
        from data_preparation.core.pipeline import Pipeline
        from data_preparation.core.config.base import Config, LLMConfig
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import Project, DatagenEvent, RainbowAnalysisJob
        
        # Create test data
        test_project_id = uuid.uuid4()
        test_datagen_event_id = uuid.uuid4()
        test_job_id = uuid.uuid4()
        
        # Sample dataset content
        sample_dataset_content = [
            {
                "samples": [
                    {
                        "type": "content",
                        "content": "Pipeline test incident report",
                        "label": "Test"
                    }
                ]
            }
        ]
        
        with get_db_session() as session:
            # Create test project, datagen event, and job
            test_project = Project(
                id=test_project_id,
                name="Pipeline Test Project",
                description="Test project for pipeline save method",
                domain="Testing"
            )
            session.add(test_project)
            
            test_datagen_event = DatagenEvent(
                id=test_datagen_event_id,
                application_description="Pipeline test application",
                domain="Testing",
                complexity=3,
                coverage=50,
                dataset_size=50,
                project_id=test_project_id
            )
            session.add(test_datagen_event)
            
            test_job = RainbowAnalysisJob(
                id=test_job_id,
                prompts=["Test prompt"],
                target_llm="test-model",
                num_samples=1,
                num_mutations=1,
                max_iters=1,
                project_id=test_project_id,
                status='running'
            )
            session.add(test_job)
            session.commit()
            
        print(f"✅ Created test data: project={test_project_id}, job={test_job_id}")
        
        # Create a mock pipeline instance
        mock_config = Config(
            description="test",
            examples=["test"],
            documents=["test"],
            task="Content",
            llm=LLMConfig(provider="test", api_key="test")
        )
        
        pipeline = Pipeline(mock_config, get_db_session)
        
        print("📝 Testing pipeline save method...")
        
        # Test the save method
        pipeline._save_generated_dataset_to_db(str(test_job_id), sample_dataset_content)
        
        print("✅ Pipeline save method completed")
        
        # Verify the data was saved
        with get_db_session() as session:
            from rainbowplus.api.models import GeneratedDataset
            
            generated_dataset = session.query(GeneratedDataset).filter(
                GeneratedDataset.job_id == test_job_id
            ).first()
            
            if generated_dataset:
                print(f"✅ GeneratedDataset created by pipeline: {generated_dataset.id}")
                print(f"   Status: {generated_dataset.generation_status}")
                print(f"   Total samples: {generated_dataset.total_samples}")
                print(f"   Linked to datagen event: {generated_dataset.datagen_event_id == test_datagen_event_id}")
                
                # Clean up
                session.delete(generated_dataset)
                
            # Clean up test data
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == test_job_id).first()
            datagen_event = session.query(DatagenEvent).filter(DatagenEvent.id == test_datagen_event_id).first()
            project = session.query(Project).filter(Project.id == test_project_id).first()
            
            if job:
                session.delete(job)
            if datagen_event:
                session.delete(datagen_event)
            if project:
                session.delete(project)
                
            session.commit()
            print("✅ Test data cleaned up")
            
            return generated_dataset is not None
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 GeneratedDataset Database Tests")
    print("Testing new database table and relationships")
    print()
    
    test1_success = test_generated_dataset_table()
    test2_success = test_pipeline_save_method()
    
    print("\n" + "=" * 50)
    if test1_success and test2_success:
        print("🎉 All tests passed!")
        print("✅ GeneratedDataset table works correctly")
        print("✅ Pipeline save method works correctly")
        print("✅ Foreign key relationships work correctly")
    else:
        print("❌ Some tests failed!")
        if not test1_success:
            print("❌ GeneratedDataset table test failed")
        if not test2_success:
            print("❌ Pipeline save method test failed")
    
    return test1_success and test2_success

if __name__ == "__main__":
    main()
