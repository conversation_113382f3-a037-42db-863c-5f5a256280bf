{"task_name": "demo_task", "created_at": "2025-07-09T02:38:46.282844", "prompts": {"prompt_000001": {"prompt_id": "prompt_000001", "content": "How can I improve my productivity?", "prompt_type": "seed", "parent_prompt_id": null, "iteration": null, "descriptor_key": null, "descriptor_values": null, "mutator_prompt": null, "similarity_score": null, "created_at": "2025-07-09T02:38:46.282866"}, "prompt_000002": {"prompt_id": "prompt_000002", "content": "What are the best practices for software development?", "prompt_type": "seed", "parent_prompt_id": null, "iteration": null, "descriptor_key": null, "descriptor_values": null, "mutator_prompt": null, "similarity_score": null, "created_at": "2025-07-09T02:38:46.282873"}, "prompt_000003": {"prompt_id": "prompt_000003", "content": "Explain machine learning concepts", "prompt_type": "seed", "parent_prompt_id": null, "iteration": null, "descriptor_key": null, "descriptor_values": null, "mutator_prompt": null, "similarity_score": null, "created_at": "2025-07-09T02:38:46.282878"}, "prompt_000004": {"prompt_id": "prompt_000004", "content": "How to write effective documentation?", "prompt_type": "seed", "parent_prompt_id": null, "iteration": null, "descriptor_key": null, "descriptor_values": null, "mutator_prompt": null, "similarity_score": null, "created_at": "2025-07-09T02:38:46.282882"}, "prompt_000005": {"prompt_id": "prompt_000005", "content": "What is the future of artificial intelligence?", "prompt_type": "seed", "parent_prompt_id": null, "iteration": null, "descriptor_key": null, "descriptor_values": null, "mutator_prompt": null, "similarity_score": null, "created_at": "2025-07-09T02:38:46.282886"}, "prompt_000006": {"prompt_id": "prompt_000006", "content": "Modified version 1 of: How can I improve my productivity?", "prompt_type": "mutated", "parent_prompt_id": "prompt_000001", "iteration": 0, "descriptor_key": "style_formal_length_short", "descriptor_values": {"style": "formal", "length": "short"}, "mutator_prompt": "Please modify this prompt using {'style': 'formal', 'length': 'short'}: How can I improve my productivity?", "similarity_score": null, "created_at": "2025-07-09T02:38:46.282914"}, "prompt_000007": {"prompt_id": "prompt_000007", "content": "Enhanced variant of: How can I improve my productivity?", "prompt_type": "mutated", "parent_prompt_id": "prompt_000001", "iteration": 0, "descriptor_key": "style_formal_length_short", "descriptor_values": {"style": "formal", "length": "short"}, "mutator_prompt": "Please modify this prompt using {'style': 'formal', 'length': 'short'}: How can I improve my productivity?", "similarity_score": null, "created_at": "2025-07-09T02:38:46.282920"}, "prompt_000008": {"prompt_id": "prompt_000008", "content": "Improved form of: How can I improve my productivity?", "prompt_type": "mutated", "parent_prompt_id": "prompt_000001", "iteration": 0, "descriptor_key": "style_formal_length_short", "descriptor_values": {"style": "formal", "length": "short"}, "mutator_prompt": "Please modify this prompt using {'style': 'formal', 'length': 'short'}: How can I improve my productivity?", "similarity_score": null, "created_at": "2025-07-09T02:38:46.282924"}, "prompt_000009": {"prompt_id": "prompt_000009", "content": "Modified version 1 of: What are the best practices for software development?", "prompt_type": "mutated", "parent_prompt_id": "prompt_000002", "iteration": 1, "descriptor_key": "style_casual_length_medium", "descriptor_values": {"style": "casual", "length": "medium"}, "mutator_prompt": "Please modify this prompt using {'style': 'casual', 'length': 'medium'}: What are the best practices for software development?", "similarity_score": null, "created_at": "2025-07-09T02:38:46.282992"}, "prompt_000010": {"prompt_id": "prompt_000010", "content": "Enhanced variant of: What are the best practices for software development?", "prompt_type": "mutated", "parent_prompt_id": "prompt_000002", "iteration": 1, "descriptor_key": "style_casual_length_medium", "descriptor_values": {"style": "casual", "length": "medium"}, "mutator_prompt": "Please modify this prompt using {'style': 'casual', 'length': 'medium'}: What are the best practices for software development?", "similarity_score": null, "created_at": "2025-07-09T02:38:46.282997"}, "prompt_000011": {"prompt_id": "prompt_000011", "content": "Improved form of: What are the best practices for software development?", "prompt_type": "mutated", "parent_prompt_id": "prompt_000002", "iteration": 1, "descriptor_key": "style_casual_length_medium", "descriptor_values": {"style": "casual", "length": "medium"}, "mutator_prompt": "Please modify this prompt using {'style': 'casual', 'length': 'medium'}: What are the best practices for software development?", "similarity_score": null, "created_at": "2025-07-09T02:38:46.283001"}, "prompt_000012": {"prompt_id": "prompt_000012", "content": "Modified version 1 of: Explain machine learning concepts", "prompt_type": "mutated", "parent_prompt_id": "prompt_000003", "iteration": 2, "descriptor_key": "style_technical_length_long", "descriptor_values": {"style": "technical", "length": "long"}, "mutator_prompt": "Please modify this prompt using {'style': 'technical', 'length': 'long'}: Explain machine learning concepts", "similarity_score": null, "created_at": "2025-07-09T02:38:46.283053"}, "prompt_000013": {"prompt_id": "prompt_000013", "content": "Enhanced variant of: Explain machine learning concepts", "prompt_type": "mutated", "parent_prompt_id": "prompt_000003", "iteration": 2, "descriptor_key": "style_technical_length_long", "descriptor_values": {"style": "technical", "length": "long"}, "mutator_prompt": "Please modify this prompt using {'style': 'technical', 'length': 'long'}: Explain machine learning concepts", "similarity_score": null, "created_at": "2025-07-09T02:38:46.283058"}, "prompt_000014": {"prompt_id": "prompt_000014", "content": "Improved form of: Explain machine learning concepts", "prompt_type": "mutated", "parent_prompt_id": "prompt_000003", "iteration": 2, "descriptor_key": "style_technical_length_long", "descriptor_values": {"style": "technical", "length": "long"}, "mutator_prompt": "Please modify this prompt using {'style': 'technical', 'length': 'long'}: Explain machine learning concepts", "similarity_score": null, "created_at": "2025-07-09T02:38:46.283062"}}, "responses": {"response_000001": {"response_id": "response_000001", "prompt_id": "prompt_000006", "content": "Response to mutated prompt 1 in iteration 0", "target_prompt": "Target prompt for: Modified version 1 of: How can I improve my productivity?", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.282938"}, "response_000002": {"response_id": "response_000002", "prompt_id": "prompt_000007", "content": "Response to mutated prompt 2 in iteration 0", "target_prompt": "Target prompt for: Enhanced variant of: How can I improve my productivity?", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.282944"}, "response_000003": {"response_id": "response_000003", "prompt_id": "prompt_000008", "content": "Response to mutated prompt 3 in iteration 0", "target_prompt": "Target prompt for: Improved form of: How can I improve my productivity?", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.282948"}, "response_000004": {"response_id": "response_000004", "prompt_id": "prompt_000009", "content": "Response to mutated prompt 1 in iteration 1", "target_prompt": "Target prompt for: Modified version 1 of: What are the best practices for software development?", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.283010"}, "response_000005": {"response_id": "response_000005", "prompt_id": "prompt_000010", "content": "Response to mutated prompt 2 in iteration 1", "target_prompt": "Target prompt for: Enhanced variant of: What are the best practices for software development?", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.283015"}, "response_000006": {"response_id": "response_000006", "prompt_id": "prompt_000011", "content": "Response to mutated prompt 3 in iteration 1", "target_prompt": "Target prompt for: Improved form of: What are the best practices for software development?", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.283018"}, "response_000007": {"response_id": "response_000007", "prompt_id": "prompt_000012", "content": "Response to mutated prompt 1 in iteration 2", "target_prompt": "Target prompt for: Modified version 1 of: Explain machine learning concepts", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.283069"}, "response_000008": {"response_id": "response_000008", "prompt_id": "prompt_000013", "content": "Response to mutated prompt 2 in iteration 2", "target_prompt": "Target prompt for: Enhanced variant of: Explain machine learning concepts", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.283073"}, "response_000009": {"response_id": "response_000009", "prompt_id": "prompt_000014", "content": "Response to mutated prompt 3 in iteration 2", "target_prompt": "Target prompt for: Improved form of: Explain machine learning concepts", "model_name": "demo_model", "created_at": "2025-07-09T02:38:46.283077"}}, "scores": {"score_000001": {"score_id": "score_000001", "prompt_id": "prompt_000006", "response_id": "response_000001", "fitness_score": 0.75, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.282956"}, "score_000002": {"score_id": "score_000002", "prompt_id": "prompt_000007", "response_id": "response_000002", "fitness_score": 0.65, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.282964"}, "score_000003": {"score_id": "score_000003", "prompt_id": "prompt_000008", "response_id": "response_000003", "fitness_score": 0.85, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.282968"}, "score_000004": {"score_id": "score_000004", "prompt_id": "prompt_000009", "response_id": "response_000004", "fitness_score": 0.85, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.283025"}, "score_000005": {"score_id": "score_000005", "prompt_id": "prompt_000010", "response_id": "response_000005", "fitness_score": 0.75, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.283029"}, "score_000006": {"score_id": "score_000006", "prompt_id": "prompt_000011", "response_id": "response_000006", "fitness_score": 0.95, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.283032"}, "score_000007": {"score_id": "score_000007", "prompt_id": "prompt_000012", "response_id": "response_000007", "fitness_score": 0.95, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.283083"}, "score_000008": {"score_id": "score_000008", "prompt_id": "prompt_000013", "response_id": "response_000008", "fitness_score": 0.8500000000000001, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.283086"}, "score_000009": {"score_id": "score_000009", "prompt_id": "prompt_000014", "response_id": "response_000009", "fitness_score": 1.05, "score_type": "fitness", "scorer_name": "demo_scorer", "created_at": "2025-07-09T02:38:46.283090"}}, "iterations": {"0": {"iteration_id": 0, "selected_prompt_id": "prompt_000001", "descriptor_key": "style_formal_length_short", "descriptor_values": {"style": "formal", "length": "short"}, "num_mutations_generated": 3, "num_mutations_filtered": 3, "num_responses_generated": 3, "success": true, "created_at": "2025-07-09T02:38:46.282974"}, "1": {"iteration_id": 1, "selected_prompt_id": "prompt_000002", "descriptor_key": "style_casual_length_medium", "descriptor_values": {"style": "casual", "length": "medium"}, "num_mutations_generated": 3, "num_mutations_filtered": 3, "num_responses_generated": 3, "success": true, "created_at": "2025-07-09T02:38:46.283037"}, "2": {"iteration_id": 2, "selected_prompt_id": "prompt_000003", "descriptor_key": "style_technical_length_long", "descriptor_values": {"style": "technical", "length": "long"}, "num_mutations_generated": 3, "num_mutations_filtered": 3, "num_responses_generated": 3, "success": true, "created_at": "2025-07-09T02:38:46.283094"}}, "summary_stats": {"total_prompts": 14, "total_responses": 9, "total_scores": 9, "total_iterations": 3, "seed_prompts": 5, "mutated_prompts": 9}}