#!/usr/bin/env python3
"""
Test script to verify the centralized configuration system works correctly.
Tests both backend configuration and frontend configuration changes.
"""

import requests
import subprocess
import os
import time
import json

def run_command(cmd, cwd=None):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def test_backend_health(url):
    """Test if backend is accessible at the given URL."""
    try:
        response = requests.get(f"{url}/health", timeout=5)
        return response.status_code == 200, response.json() if response.status_code == 200 else None
    except Exception as e:
        return False, str(e)

def test_frontend_config_change():
    """Test changing frontend configuration."""
    print("🧪 Testing Frontend Configuration Changes")
    print("=" * 50)
    
    client_dir = "client"
    
    # Test 1: Check current configuration
    print("\n📋 Test 1: Check Current Configuration")
    success, output, error = run_command("./configure-backend.sh --current", cwd=client_dir)
    if success:
        print("✅ Configuration script works")
        print(f"   Output: {output.strip()}")
    else:
        print(f"❌ Configuration script failed: {error}")
        return False
    
    # Test 2: Change to a test configuration
    print("\n📋 Test 2: Change Configuration")
    test_url = "http://*************:8000"
    success, output, error = run_command(f"./configure-backend.sh {test_url}", cwd=client_dir)
    if success:
        print(f"✅ Successfully changed configuration to {test_url}")
    else:
        print(f"❌ Failed to change configuration: {error}")
        return False
    
    # Test 3: Verify the change
    print("\n📋 Test 3: Verify Configuration Change")
    success, output, error = run_command("grep NEXT_PUBLIC_API_URL .env.local", cwd=client_dir)
    if success and test_url in output:
        print("✅ Configuration file updated correctly")
    else:
        print(f"❌ Configuration file not updated correctly: {output}")
        return False
    
    # Test 4: Test preset functionality
    print("\n📋 Test 4: Test Preset Functionality")
    success, output, error = run_command("./configure-backend.sh local", cwd=client_dir)
    if success:
        print("✅ Preset configuration works")
    else:
        print(f"❌ Preset configuration failed: {error}")
        return False
    
    # Test 5: Verify preset change
    print("\n📋 Test 5: Verify Preset Change")
    success, output, error = run_command("grep NEXT_PUBLIC_API_URL .env.local", cwd=client_dir)
    if success and "localhost:8000" in output:
        print("✅ Preset configuration applied correctly")
    else:
        print(f"❌ Preset configuration not applied: {output}")
        return False
    
    return True

def test_api_configuration():
    """Test API configuration with different backend URLs."""
    print("\n🧪 Testing API Configuration")
    print("=" * 50)
    
    # Test current backend
    print("\n📋 Test 1: Current Backend Connection")
    backend_url = "http://localhost:8000"
    
    healthy, response = test_backend_health(backend_url)
    if healthy:
        print(f"✅ Backend is healthy at {backend_url}")
        print(f"   Response: {response}")
    else:
        print(f"⚠️  Backend not accessible at {backend_url}: {response}")
        print("   This is expected if backend is not running")
    
    return True

def test_configuration_files():
    """Test that all configuration files exist and are valid."""
    print("\n🧪 Testing Configuration Files")
    print("=" * 50)
    
    client_dir = "client"
    required_files = [
        ".env.local",
        ".env.example", 
        "lib/config.ts",
        "lib/api-client.ts",
        "next.config.mjs",
        "configure-backend.sh",
        "scripts/configure-backend.js",
        "CONFIG.md"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = os.path.join(client_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def test_environment_variables():
    """Test environment variable configuration."""
    print("\n🧪 Testing Environment Variables")
    print("=" * 50)
    
    client_dir = "client"
    
    # Check .env.local content
    env_file = os.path.join(client_dir, ".env.local")
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            content = f.read()
            
        required_vars = [
            "NEXT_PUBLIC_API_URL",
            "PORT",
            "NODE_ENV"
        ]
        
        all_vars_present = True
        for var in required_vars:
            if var in content:
                # Extract the value
                for line in content.split('\n'):
                    if line.startswith(f"{var}="):
                        value = line.split('=', 1)[1]
                        print(f"✅ {var}={value}")
                        break
            else:
                print(f"❌ {var} not found in .env.local")
                all_vars_present = False
        
        return all_vars_present
    else:
        print("❌ .env.local file not found")
        return False

def main():
    """Run all configuration tests."""
    print("🚀 RainbowPlus Centralized Configuration Test")
    print("=" * 60)
    
    tests = [
        ("Configuration Files", test_configuration_files),
        ("Environment Variables", test_environment_variables),
        ("Frontend Configuration", test_frontend_config_change),
        ("API Configuration", test_api_configuration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎯 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All configuration tests passed!")
        print("\nYour centralized configuration system is working correctly.")
        print("\nTo change the backend IP:")
        print("  cd client")
        print("  ./configure-backend.sh http://your-backend-ip:8000")
        print("  ./run-dev.sh")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
