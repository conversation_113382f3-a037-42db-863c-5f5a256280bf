#!/usr/bin/env python3
"""
Test script for the expert review API endpoints to verify the new functionality.
"""

import requests
import json

# API endpoints
BASE_URL = "http://localhost:8000"

def test_expert_review_functionality():
    """Test the complete expert review functionality."""
    
    print("🧪 Testing Expert Review API Functionality")
    print("=" * 60)
    
    # Test 1: List all datagen events
    print("📝 Test 1: List all datagen events...")
    try:
        response = requests.get(f"{BASE_URL}/datagen/events?limit=5")
        response.raise_for_status()
        data = response.json()
        print(f"✅ Success! Found {data['total_count']} total events")
        print(f"   Returned {len(data['events'])} events in this page")
        
        if data['events']:
            first_event = data['events'][0]
            print(f"   First event ID: {first_event['id']}")
            print(f"   Domain: {first_event['domain']}")
            print(f"   Selected experts: {first_event['selected_experts']}")
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    # Test 2: Filter events by expert
    print("\n📝 Test 2: Filter events by expert_003...")
    try:
        response = requests.get(f"{BASE_URL}/datagen/events?expert_id=expert_003&limit=5")
        response.raise_for_status()
        data = response.json()
        print(f"✅ Success! Found {data['total_count']} events for expert_003")
        
        if data['events']:
            for event in data['events']:
                print(f"   Event: {event['application_description'][:50]}...")
                print(f"   Domain: {event['domain']}")
                print(f"   Experts: {event['selected_experts']}")
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    # Test 3: Filter events by domain
    print("\n📝 Test 3: Filter events by domain...")
    try:
        response = requests.get(f"{BASE_URL}/datagen/events?domain=legal&limit=5")
        response.raise_for_status()
        data = response.json()
        print(f"✅ Success! Found {data['total_count']} events in legal domain")
        
        if data['events']:
            for event in data['events']:
                print(f"   Event: {event['application_description'][:50]}...")
                print(f"   Domain: {event['domain']}")
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    # Test 4: List dataset samples
    print("\n📝 Test 4: List dataset samples...")
    try:
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=3")
        response.raise_for_status()
        data = response.json()
        print(f"✅ Success! Found {data['total_count']} total samples")
        print(f"   Returned {len(data['samples'])} samples in this page")
        
        if data['samples']:
            first_sample = data['samples'][0]
            print(f"   First sample ID: {first_sample['id']}")
            print(f"   Label: {first_sample['label']}")
            print(f"   Content preview: {first_sample['content'][:100]}...")
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    # Test 5: Filter samples by datagen event
    print("\n📝 Test 5: Filter samples by datagen event...")
    try:
        # First get an event ID
        events_response = requests.get(f"{BASE_URL}/datagen/events?limit=1")
        events_response.raise_for_status()
        events_data = events_response.json()
        
        if events_data['events']:
            event_id = events_data['events'][0]['id']
            response = requests.get(f"{BASE_URL}/datasets/samples?datagen_event_id={event_id}&limit=3")
            response.raise_for_status()
            data = response.json()
            print(f"✅ Success! Found {data['total_count']} samples for event {event_id}")
        else:
            print("⚠️ No events found to test sample filtering")
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    print("\n🎉 All expert review API tests completed successfully!")
    return True

def test_frontend_integration():
    """Test that the frontend can access the API through the proxy."""
    
    print("\n🌐 Testing Frontend Integration")
    print("=" * 40)
    
    # Test frontend API proxy
    print("📝 Testing frontend API proxy...")
    try:
        # This should go through the Next.js API proxy
        response = requests.get("http://localhost:3002/api/datagen/events?limit=1", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Frontend proxy working! Found {data.get('total_count', 0)} events")
        else:
            print(f"⚠️ Frontend proxy returned status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️ Frontend server not running on port 3002")
    except Exception as e:
        print(f"❌ Frontend proxy test failed: {e}")

if __name__ == "__main__":
    success = test_expert_review_functionality()
    test_frontend_integration()
    
    if success:
        print("\n✅ Expert review feature is working correctly!")
        print("🌐 You can now test the UI at: http://localhost:3002")
        print("📋 Navigate to the 'Expert Review' section in the sidebar")
    else:
        print("\n❌ Some tests failed. Please check the API server and database.")
