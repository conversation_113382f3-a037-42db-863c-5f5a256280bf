#!/usr/bin/env python3
"""
Quick fix script for the database column issue.
This script adds the missing 'nickname' column to the rainbow_analysis_jobs table.

Usage:
    python fix_database.py
"""

import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from sqlalchemy import create_engine, text
    from sqlalchemy.exc import OperationalError, ProgrammingError
    import logging
except ImportError as e:
    print(f"❌ Missing required packages: {e}")
    print("💡 Install with: pip install sqlalchemy psycopg2-binary")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_database_url():
    """Get database URL from environment variables."""
    # Default values for local development (matching docker-compose.yml)
    host = os.environ.get('POSTGRES_HOST', 'localhost')
    port = os.environ.get('POSTGRES_PORT', '5432')
    user = os.environ.get('POSTGRES_USER', 'myuser')
    password = os.environ.get('POSTGRES_PASSWORD', 'mypassword')
    database = os.environ.get('POSTGRES_DB', 'mydb')
    
    return f"postgresql://{user}:{password}@{host}:{port}/{database}"


def check_and_add_nickname_column():
    """Check if nickname column exists and add it if missing."""
    database_url = get_database_url()
    logger.info(f"🔗 Connecting to database...")
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # Test connection
            conn.execute(text("SELECT 1"))
            logger.info("✅ Database connection successful")
            
            # Check if nickname column exists
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'rainbow_analysis_jobs' 
                AND column_name = 'nickname'
            """))
            
            if result.fetchone():
                logger.info("✅ Column 'nickname' already exists")
                return True
            
            # Add the nickname column
            logger.info("🔧 Adding 'nickname' column...")
            conn.execute(text("""
                ALTER TABLE rainbow_analysis_jobs 
                ADD COLUMN nickname VARCHAR(255)
            """))
            conn.commit()
            
            logger.info("✅ Successfully added 'nickname' column!")
            return True
            
    except OperationalError as e:
        logger.error(f"❌ Database connection failed: {e}")
        logger.info("💡 Make sure PostgreSQL is running:")
        logger.info("   docker-compose up postgres")
        return False
    except ProgrammingError as e:
        if "does not exist" in str(e) and "rainbow_analysis_jobs" in str(e):
            logger.error("❌ Table 'rainbow_analysis_jobs' does not exist")
            logger.info("💡 You may need to create the database schema first")
        else:
            logger.error(f"❌ Database error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False


def main():
    """Main function."""
    print("🚀 RainbowPlus Database Fix")
    print("   Adding missing 'nickname' column for Slack notifications")
    print()
    
    success = check_and_add_nickname_column()
    
    if success:
        print()
        print("🎉 Database fix completed successfully!")
        print("💡 You can now use the 'nickname' parameter for Slack notifications")
        print()
        print("Example usage:")
        print('   curl -X POST "http://localhost:8000/rainbowplus" \\')
        print('        -H "Content-Type: application/json" \\')
        print('        -d \'{"prompts": ["test"], "target_llm": "gpt-3.5-turbo", "nickname": "your_name"}\'')
    else:
        print()
        print("❌ Database fix failed")
        print("💡 Check the error messages above for troubleshooting")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
