import sys
import os

# Add data_preparation directory to sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../data_preparation')))

from data_preparation.core.config.base import Config<PERSON>oader
from data_preparation.core.pipeline import Pipeline
from rainbowplus.api.database import get_db_session
from rainbowplus.api.models import RainbowAnalysisJob

def run_pipeline_and_check_db(config_path: str):
    # Load config
    config = ConfigLoader.load(config_path)

    # Create a new job record in the database
    with get_db_session() as session:
        job = RainbowAnalysisJob(
            prompts=["Example prompt for testing"],
            target_llm="test-llm",
            num_samples=1,
            num_mutations=1,
            max_iters=1,
            status="pending"
        )
        session.add(job)
        session.flush()
        job_id = job.id
        print(f"Created job with ID: {job_id}")

    # Create pipeline instance with config
    pipeline = Pipeline(config)

    # Run generate with job_id to write results to database
    samples = pipeline.generate(job_id=job_id)
    print(f"Generated samples: {samples}")

    # Query the database to verify the job record is updated
    with get_db_session() as session:
        updated_job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
        if updated_job:
            print(f"Job status: {updated_job.status}")
            print(f"Job results: {updated_job.results}")
        else:
            print("Job not found in database.")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 run_pipeline_and_check_db.py <config_path>")
        sys.exit(1)
    config_path = sys.argv[1]
    run_pipeline_and_check_db(config_path)