#!/usr/bin/env python3
"""
Test script to verify JSON serialization of PointResponse objects.
This helps ensure the database storage won't fail with serialization errors.
"""

import sys
from pathlib import Path
import json

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rainbowplus.api.models import PointResponse
from rainbowplus.api.tasks import make_json_serializable


def test_point_response_serialization():
    """Test that PointResponse objects can be serialized to JSON."""
    
    print("🧪 Testing PointResponse JSON serialization...")
    
    # Create a sample PointResponse object
    point = PointResponse(
        adv_prompt="How can I create a secure password?",
        response="To create a secure password, use a combination of...",
        score=0.0234,
        descriptor="Security, Best Practices"
    )
    
    print(f"   Original PointResponse: {point}")
    
    # Test direct JSON serialization (this should fail)
    try:
        json.dumps(point)
        print("❌ Direct JSON serialization unexpectedly succeeded")
        return False
    except TypeError as e:
        print(f"✅ Direct JSON serialization failed as expected: {e}")
    
    # Test our helper function
    try:
        serializable_point = make_json_serializable(point)
        print(f"   Serializable format: {serializable_point}")
        
        # Try to serialize the result
        json_str = json.dumps(serializable_point, indent=2)
        print("✅ Helper function serialization succeeded!")
        print(f"   JSON output: {json_str}")
        
        # Test deserialization
        deserialized = json.loads(json_str)
        print(f"✅ Deserialization succeeded: {deserialized}")
        
        return True
        
    except Exception as e:
        print(f"❌ Helper function serialization failed: {e}")
        return False


def test_complex_result_serialization():
    """Test serialization of a complete result structure."""
    
    print("\n🧪 Testing complex result structure serialization...")
    
    # Create sample data similar to what the task returns
    points = [
        PointResponse(
            adv_prompt="Test prompt 1",
            response="Test response 1",
            score=0.0123,
            descriptor="Category A"
        ),
        PointResponse(
            adv_prompt="Test prompt 2", 
            response="Test response 2",
            score=0.0456,
            descriptor="Category B"
        )
    ]
    
    scores = {
        ("Category A", "Style 1"): [0.0123, 0.0234, 0.0345],
        ("Category B", "Style 2"): [0.0456, 0.0567, 0.0678]
    }
    
    result = {
        "success": True,
        "points": points,  # This contains PointResponse objects
        "scores": scores,
        "message": "Analysis completed successfully"
    }
    
    print(f"   Original result structure has {len(points)} PointResponse objects")
    
    try:
        serializable_result = make_json_serializable(result)
        json_str = json.dumps(serializable_result, indent=2)
        print("✅ Complex result serialization succeeded!")
        
        # Verify the structure is preserved
        deserialized = json.loads(json_str)
        assert deserialized["success"] == True
        assert len(deserialized["points"]) == 2
        assert "scores" in deserialized
        print("✅ Structure verification passed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Complex result serialization failed: {e}")
        return False


def test_tuple_keys_serialization():
    """Test serialization of dictionaries with tuple keys."""

    print("\n🧪 Testing tuple keys serialization...")

    # This is the exact format that causes the error
    scores_with_tuples = {
        ('Criminal Planning', 'Slang'): [0.0123, 0.0234, 0.0345],
        ('Fraud and Scams', 'Hypotheticals'): [0.0456, 0.0567, 0.0678]
    }

    print(f"   Original scores dict has tuple keys: {list(scores_with_tuples.keys())}")

    try:
        # This should fail with direct JSON serialization
        json.dumps(scores_with_tuples)
        print("❌ Direct JSON serialization unexpectedly succeeded")
        return False
    except TypeError as e:
        print(f"✅ Direct JSON serialization failed as expected: {e}")

    try:
        serializable_scores = make_json_serializable(scores_with_tuples)
        json_str = json.dumps(serializable_scores, indent=2)
        print("✅ Tuple keys serialization succeeded!")
        print(f"   Converted keys: {list(serializable_scores.keys())}")

        return True

    except Exception as e:
        print(f"❌ Tuple keys serialization failed: {e}")
        return False


def main():
    """Run all serialization tests."""
    print("🚀 JSON Serialization Tests for RainbowPlus")
    print("   Testing database storage compatibility...")
    print()

    test1_passed = test_point_response_serialization()
    test2_passed = test_complex_result_serialization()
    test3_passed = test_tuple_keys_serialization()

    print("\n📊 Test Results:")
    print(f"   PointResponse serialization: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Complex result serialization: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Tuple keys serialization: {'✅ PASS' if test3_passed else '❌ FAIL'}")

    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 All tests passed! Database storage should work correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
