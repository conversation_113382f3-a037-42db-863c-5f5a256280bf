#!/usr/bin/env python3
"""
Test dataset generation using original base.yml settings (no parameter overrides).
"""

import requests
import json
import time

# API endpoints
BASE_URL = "http://localhost:8000"
DATAGEN_EVENT_URL = f"{BASE_URL}/datagen/events"
DATASET_GENERATION_URL = f"{BASE_URL}/datasets/generate"

def test_original_dataset_generation():
    """Test dataset generation using original base.yml settings."""
    
    print("🧪 Testing Dataset Generation with Original Settings")
    print("=" * 60)
    
    # Step 1: Create a datagen event
    print("📝 Step 1: Creating datagen event...")
    
    datagen_event_data = {
        "application_description": "Test application for incident reporting with original settings",
        "domain": "Public Safety",
        "example_input": "Report suspicious activity in the park",
        "test_types": [
            {"id": "harassment", "label": "Harassment", "checked": True},
            {"id": "cheating", "label": "Cheating", "checked": True}
        ],
        "complexity": 5,
        "coverage": 70,
        "dataset_size": 100,
        "selected_experts": ["safety_expert", "legal_expert"],
        "uploaded_files": ["test_file.txt"],
        "user_session_id": "test-session-original-123",
        "project_id": None
    }
    
    try:
        event_response = requests.post(DATAGEN_EVENT_URL, json=datagen_event_data)
        event_response.raise_for_status()
        event_result = event_response.json()
        print(f"✅ Datagen event created: {event_result}")
        event_id = event_result.get('event_id')
    except Exception as e:
        print(f"❌ Failed to create datagen event: {e}")
        return False
    
    # Step 2: Generate dataset using base.yml settings (no overrides)
    print("\n🚀 Step 2: Generating dataset with base.yml settings...")
    
    # Use the API key from base.yml but don't override sampling_params
    api_key = "********************************************************************************************************************************************************************"
    
    dataset_generation_data = {
        "description": "Test application for incident reporting with original settings",
        "examples": ["Report suspicious activity in the park"],
        "task": "Content",
        "llm": {
            "provider": "openai",
            "api_key": api_key,
            "model_kwargs": {
                "model": "gpt-4o-mini"
            }
            # No sampling_params override - use base.yml defaults (max_tokens: 8048, n: 12)
        },
        "nickname": "original-test-user",
        "project_id": None
    }
    
    try:
        dataset_response = requests.post(DATASET_GENERATION_URL, json=dataset_generation_data)
        dataset_response.raise_for_status()
        dataset_result = dataset_response.json()
        print(f"✅ Dataset generation started: {dataset_result}")
        job_id = dataset_result.get('job_id')
    except Exception as e:
        print(f"❌ Failed to start dataset generation: {e}")
        return False
    
    # Step 3: Wait for completion
    print(f"\n⏳ Step 3: Waiting for completion (job_id: {job_id})...")
    print("   Using original base.yml settings: max_tokens=8048, n=12")
    print("   (This should work without token limit issues)")
    
    # Wait for the generation to complete (may take longer with 12 samples)
    for i in range(12):  # Wait up to 60 seconds
        time.sleep(5)
        
        try:
            from rainbowplus.api.database import get_db_session
            from rainbowplus.api.models import RainbowAnalysisJob, GeneratedDataset
            
            with get_db_session() as session:
                job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
                if job:
                    print(f"   Job status: {job.status}")
                    
                    if job.status == 'completed':
                        print("✅ Job completed successfully!")
                        break
                    elif job.status == 'failed':
                        print(f"❌ Job failed: {job.results}")
                        return False
                else:
                    print("❌ Job not found")
                    return False
        except Exception as e:
            print(f"❌ Error checking job status: {e}")
            return False
    
    # Step 4: Check final results
    print("\n🔍 Step 4: Checking final results...")
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import RainbowAnalysisJob, GeneratedDataset
        
        with get_db_session() as session:
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
            if job:
                print(f"📊 Final job status: {job.status}")
                print(f"📊 Job has results: {bool(job.results)}")
                
                if job.results and isinstance(job.results, dict) and 'error' in job.results:
                    print(f"📊 Job error: {job.results['error']}")
                elif job.results:
                    print(f"📊 Results size: {len(str(job.results))} chars")
                    print(f"📊 Results type: {type(job.results)}")
                
                # Check for GeneratedDataset (this is what we're testing!)
                generated_dataset = session.query(GeneratedDataset).filter(
                    GeneratedDataset.job_id == job_id
                ).first()
                
                if generated_dataset:
                    print(f"\n🎉 SUCCESS: GeneratedDataset found!")
                    print(f"   ID: {generated_dataset.id}")
                    print(f"   Status: {generated_dataset.generation_status}")
                    print(f"   Dataset name: {generated_dataset.dataset_name}")
                    print(f"   Total samples: {generated_dataset.total_samples}")
                    print(f"   Sample groups: {generated_dataset.sample_groups}")
                    print(f"   Datagen event ID: {generated_dataset.datagen_event_id}")
                    print(f"   Project ID: {generated_dataset.project_id}")
                    print(f"   Job ID: {generated_dataset.job_id}")
                    print(f"   Created: {generated_dataset.created_at}")
                    print(f"   Completed: {generated_dataset.completed_at}")
                    
                    if generated_dataset.dataset_content:
                        print(f"\n📄 Dataset content verification:")
                        print(f"   Content size: {len(str(generated_dataset.dataset_content))} chars")
                        print(f"   Content type: {type(generated_dataset.dataset_content)}")
                        
                        # Show a preview of the content
                        content_preview = str(generated_dataset.dataset_content)[:300]
                        print(f"   Content preview: {content_preview}...")
                        
                        # Verify structure
                        if isinstance(generated_dataset.dataset_content, list) and len(generated_dataset.dataset_content) > 0:
                            first_group = generated_dataset.dataset_content[0]
                            if isinstance(first_group, dict) and 'samples' in first_group:
                                print(f"   ✅ Dataset structure is correct")
                                print(f"   Samples in first group: {len(first_group['samples'])}")
                            else:
                                print(f"   ⚠️  Dataset structure may be different than expected")
                        else:
                            print(f"   ⚠️  Dataset content format is unexpected")
                    
                    print(f"\n🔗 Relationship verification:")
                    if generated_dataset.datagen_event_id:
                        print(f"   ✅ Linked to datagen event: {generated_dataset.datagen_event_id}")
                    else:
                        print(f"   ⚠️  Not linked to datagen event")
                    
                    if generated_dataset.job_id == job_id:
                        print(f"   ✅ Correctly linked to job: {job_id}")
                    else:
                        print(f"   ❌ Job link mismatch")
                    
                    return True
                else:
                    print("❌ No GeneratedDataset found for this job")
                    print("💡 The new save method may not be working correctly")
                    print("💡 Check server logs for debug messages")
                    return False
            else:
                print(f"❌ Job {job_id} not found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking final results: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 Original Dataset Generation Test")
    print("Testing with base.yml settings (no parameter overrides)")
    print()
    
    success = test_original_dataset_generation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test successful!")
        print("✅ Dataset generation works with original settings")
        print("✅ GeneratedDataset table is populated correctly")
        print("✅ New database save functionality is working")
        print("✅ Foreign key relationships are established")
    else:
        print("❌ Test failed!")
        print("💡 Check server logs for debug messages")
        print("💡 The issue may be in the LLM API or database save")
    
    return success

if __name__ == "__main__":
    main()
