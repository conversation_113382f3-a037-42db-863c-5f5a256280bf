CREATE TABLE api_configs (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL,
    endpoint_ref VARCHAR(255) NOT NULL, -- Reference to secret (e.g., Vault path or encrypted key)
    status VARCHAR(50) NOT NULL, -- ACTIVE, SUSPENDED, REMOVED
    priority INTEGER DEFAULT 0,
    retry_count INTEGER DEFAULT 3,
    last_failure TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Optional audit table for tracking changes
CREATE TABLE api_config_audit (
    audit_id SERIAL PRIMARY KEY,
    api_id INTEGER REFERENCES api_configs(id),
    operation VARCHAR(50),
    changed_by VA<PERSON><PERSON><PERSON>(255),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);