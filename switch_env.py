#!/usr/bin/env python3
"""
Environment switcher for RainbowPlus development.
Easily switch between local Docker and Azure PostgreSQL configurations.

Usage:
    python switch_env.py local    # Switch to local Docker
    python switch_env.py azure    # Switch to Azure PostgreSQL
    python switch_env.py status   # Show current environment
"""

import os
import sys
import shutil
from pathlib import Path


def switch_environment(env_name: str):
    """Switch to the specified environment."""
    
    env_file = Path(f".env.{env_name}")
    target_file = Path(".env")
    
    if not env_file.exists():
        print(f"❌ Environment file not found: {env_file}")
        print("💡 Available environments:")
        for env in Path(".").glob(".env.*"):
            if env.name != ".env":
                env_type = env.name.replace(".env.", "")
                print(f"   {env_type}")
        return False
    
    # Backup current .env if it exists
    if target_file.exists():
        backup_file = Path(".env.backup")
        shutil.copy2(target_file, backup_file)
        print(f"📁 Backed up current .env to {backup_file}")
    
    # Copy environment file
    shutil.copy2(env_file, target_file)
    print(f"✅ Switched to {env_name} environment")
    
    # Show key settings
    print(f"\n📊 Environment settings:")
    with open(env_file) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                if key in ['POSTGRES_HOST', 'POSTGRES_SSL', 'POSTGRES_USER']:
                    # Mask password for security
                    if 'PASSWORD' in key:
                        value = '*' * len(value)
                    print(f"   {key}: {value}")
    
    return True


def show_status():
    """Show current environment status."""
    
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ No .env file found")
        print("💡 Run: python switch_env.py <environment>")
        return
    
    print("📊 Current Environment Status:")
    
    # Try to determine environment type
    postgres_host = None
    postgres_ssl = None
    
    with open(env_file) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                if key == 'POSTGRES_HOST':
                    postgres_host = value
                elif key == 'POSTGRES_SSL':
                    postgres_ssl = value
    
    # Determine environment type
    if postgres_host:
        if 'localhost' in postgres_host:
            env_type = "🐳 Local Docker"
        elif 'azure' in postgres_host.lower():
            env_type = "☁️  Azure PostgreSQL"
        else:
            env_type = "🔧 Custom"
        
        print(f"   Environment: {env_type}")
        print(f"   Database Host: {postgres_host}")
        print(f"   SSL Enabled: {postgres_ssl}")
    else:
        print("   ❌ Invalid .env file")


def test_connection():
    """Test database connection with current environment."""
    
    print("🔗 Testing database connection...")
    
    try:
        # Import here to use current environment
        sys.path.insert(0, str(Path(__file__).parent))
        from rainbowplus.api.database import engine
        
        with engine.connect() as conn:
            result = conn.execute("SELECT version()")
            version = result.fetchone()[0]
            print("✅ Database connection successful!")
            
            if 'azure' in version.lower():
                print("☁️  Connected to Azure PostgreSQL")
            else:
                print("🐳 Connected to local PostgreSQL")
            
            return True
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("💡 Make sure the database is running and credentials are correct")
        return False


def main():
    """Main function."""
    
    if len(sys.argv) != 2:
        print("🚀 RainbowPlus Environment Switcher")
        print()
        print("Usage:")
        print("   python switch_env.py local       # Switch to local Docker")
        print("   python switch_env.py azure       # Switch to Azure PostgreSQL")
        print("   python switch_env.py deepassure  # Switch to DeepAssure Azure DB")
        print("   python switch_env.py status      # Show current environment")
        print("   python switch_env.py test        # Test database connection")
        print()
        show_status()
        return
    
    command = sys.argv[1].lower()
    
    if command == "status":
        show_status()
    elif command == "test":
        test_connection()
    elif command in ["local", "azure", "deepassure"]:
        if switch_environment(command):
            print()
            print("💡 Next steps:")
            if command == "local":
                print("   1. Start Docker: docker-compose up postgres redis")
                print("   2. Setup database: python setup_database.py --env local")
            elif command == "deepassure":
                print("   1. Update .env.deepassure with your Azure credentials")
                print("   2. Run diagnostics: python diagnose_azure_connection.py")
                print("   3. Setup database: python setup_database.py --env-file .env.deepassure")
            else:
                print("   1. Update .env.azure with your Azure credentials")
                print("   2. Setup database: python setup_database.py --env azure")
            print("   4. Start API: uvicorn rainbowplus.api.app:app --host 0.0.0.0 --port 8000")
    else:
        print(f"❌ Unknown command: {command}")
        print("💡 Available commands: local, azure, status, test")


if __name__ == "__main__":
    main()
