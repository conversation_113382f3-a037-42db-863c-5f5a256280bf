#!/usr/bin/env python3
"""
Test script for RainbowPlus health check endpoints.
Verifies all health check functionality before deployment.

Usage:
    python test_health_checks.py
    python test_health_checks.py --url http://your-api-url.com
"""

import requests
import argparse
import json
import time
from datetime import datetime


def test_endpoint(url: str, endpoint: str, timeout: int = 10):
    """Test a health check endpoint."""
    full_url = f"{url.rstrip('/')}{endpoint}"
    
    try:
        print(f"🔍 Testing {endpoint}...")
        start_time = time.time()
        
        response = requests.get(full_url, timeout=timeout)
        response_time = time.time() - start_time
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Time: {response_time:.3f}s")
        
        if response.status_code in [200, 503]:  # 503 is acceptable for unhealthy but responding
            try:
                data = response.json()
                status = data.get('status', 'unknown')
                print(f"   Health Status: {status}")
                
                if endpoint == '/health/detailed' and 'checks' in data:
                    checks = data['checks']
                    print(f"   Components:")
                    for component, check_data in checks.items():
                        comp_status = check_data.get('status', 'unknown')
                        print(f"     {component}: {comp_status}")
                
                return True, status, response_time
                
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON response")
                return False, 'invalid', response_time
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            return False, 'error', response_time
            
    except requests.exceptions.Timeout:
        print(f"   ❌ Timeout after {timeout}s")
        return False, 'timeout', timeout
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Connection failed")
        return False, 'connection_error', 0
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False, 'error', 0


def test_all_health_checks(base_url: str):
    """Test all health check endpoints."""
    
    print("🚀 Testing RainbowPlus Health Check Endpoints")
    print(f"   Base URL: {base_url}")
    print(f"   Timestamp: {datetime.now().isoformat()}")
    print("=" * 60)
    
    endpoints = [
        ("/health", "Basic health check"),
        ("/health/detailed", "Detailed health check"),
        ("/health/database", "Database health check"),
        ("/health/celery", "Celery health check"),
        ("/health/redis", "Redis health check")
    ]
    
    results = []
    
    for endpoint, description in endpoints:
        print(f"\n📊 {description}")
        success, status, response_time = test_endpoint(base_url, endpoint)
        
        results.append({
            'endpoint': endpoint,
            'description': description,
            'success': success,
            'status': status,
            'response_time': response_time
        })
        
        if success:
            print(f"   ✅ Success")
        else:
            print(f"   ❌ Failed")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Health Check Summary")
    print("=" * 60)
    
    successful_checks = sum(1 for r in results if r['success'])
    total_checks = len(results)
    
    print(f"Successful: {successful_checks}/{total_checks}")
    print(f"Overall: {'✅ PASS' if successful_checks == total_checks else '❌ FAIL'}")
    
    print("\nDetailed Results:")
    for result in results:
        status_icon = "✅" if result['success'] else "❌"
        print(f"  {status_icon} {result['endpoint']}: {result['status']} ({result['response_time']:.3f}s)")
    
    # Recommendations
    print("\n💡 Recommendations:")
    
    failed_checks = [r for r in results if not r['success']]
    if failed_checks:
        print("   Failed health checks detected:")
        for check in failed_checks:
            print(f"   - {check['endpoint']}: {check['status']}")
        
        print("\n   Troubleshooting steps:")
        print("   1. Check if the API server is running")
        print("   2. Verify database connectivity")
        print("   3. Ensure Redis is accessible")
        print("   4. Check Celery workers are running")
        print("   5. Review application logs")
    else:
        print("   ✅ All health checks passed!")
        print("   🚀 Service is ready for production deployment")
    
    return successful_checks == total_checks


def test_azure_health_probe_simulation():
    """Simulate Azure health probe behavior."""
    print("\n🔄 Simulating Azure Health Probe Behavior")
    print("=" * 60)
    
    # Simulate liveness probe (basic health check every 30s)
    print("📍 Liveness Probe Simulation (5 checks, 2s interval)")
    liveness_results = []
    
    for i in range(5):
        print(f"   Check {i+1}/5...")
        success, status, response_time = test_endpoint(args.url, "/health", timeout=5)
        liveness_results.append(success)
        
        if i < 4:  # Don't sleep after last check
            time.sleep(2)
    
    liveness_success_rate = sum(liveness_results) / len(liveness_results) * 100
    print(f"   Liveness Success Rate: {liveness_success_rate:.1f}%")
    
    # Simulate readiness probe (detailed health check every 10s)
    print("\n📍 Readiness Probe Simulation (3 checks, 3s interval)")
    readiness_results = []
    
    for i in range(3):
        print(f"   Check {i+1}/3...")
        success, status, response_time = test_endpoint(args.url, "/health/detailed", timeout=10)
        readiness_results.append(success and status == 'healthy')
        
        if i < 2:  # Don't sleep after last check
            time.sleep(3)
    
    readiness_success_rate = sum(readiness_results) / len(readiness_results) * 100
    print(f"   Readiness Success Rate: {readiness_success_rate:.1f}%")
    
    # Overall assessment
    print(f"\n📊 Azure Probe Assessment:")
    print(f"   Liveness: {'✅ PASS' if liveness_success_rate >= 80 else '❌ FAIL'}")
    print(f"   Readiness: {'✅ PASS' if readiness_success_rate >= 80 else '❌ FAIL'}")
    
    return liveness_success_rate >= 80 and readiness_success_rate >= 80


def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test RainbowPlus health check endpoints")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL of the API")
    parser.add_argument("--azure-simulation", action="store_true", help="Run Azure probe simulation")
    parser.add_argument("--exit-code", action="store_true", help="Exit with non-zero code if tests fail")
    
    global args
    args = parser.parse_args()
    
    # Test all health checks
    all_passed = test_all_health_checks(args.url)
    
    # Azure simulation if requested
    azure_passed = True
    if args.azure_simulation:
        azure_passed = test_azure_health_probe_simulation()
    
    # Final result
    overall_passed = all_passed and azure_passed
    
    print(f"\n🎯 Final Result: {'✅ ALL TESTS PASSED' if overall_passed else '❌ SOME TESTS FAILED'}")
    
    if args.exit_code:
        exit(0 if overall_passed else 1)
    
    return overall_passed


if __name__ == "__main__":
    main()
