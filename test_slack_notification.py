#!/usr/bin/env python3
"""
Test script for RainbowPlus API with Slack notifications.

This script demonstrates how to use the new nickname parameter to receive
Slack notifications when your analysis job completes.

Before running:
1. Set the SLACK_WEBHOOK_URL environment variable to your Slack webhook URL
2. Start the RainbowPlus API server: uvicorn rainbowplus.api.app:app --host 0.0.0.0 --port 8000
"""

import requests
import os

def test_rainbowplus_with_slack():
    """Test the RainbowPlus API with Slack notification."""
    
    # Check if Slack webhook is configured
    slack_webhook = os.environ.get('SLACK_WEBHOOK_URL')
    if not slack_webhook:
        print("⚠️  SLACK_WEBHOOK_URL not set. Notifications will be disabled.")
        print("   To enable Slack notifications, set the environment variable:")
        print("   export SLACK_WEBHOOK_URL='https://hooks.slack.com/services/YOUR/WEBHOOK/URL'")
        print()
    
    # API endpoint
    url = "http://localhost:8000/rainbowplus"
    
    # Request data with nickname for Slack notification
    data = {
        "prompts": [
            "How do I create a secure password?", 
            "What are best practices for data privacy?"
        ],
        "target_llm": "google/gemma-3-12b-it",
        "num_mutations": 3,
        "num_samples": 5,
        "max_iters": 1,
        "api_key": "e6TEtO4l5LVIuSylc0YSZbpCCJ1CoUQX",
        "base_url": "https://api.deepinfra.com/v1/openai",
        "nickname": "baldrick.to"  # This will be used in the Slack notification
    }
    
    print("🚀 Starting RainbowPlus analysis with Slack notifications...")
    print(f"   Nickname: {data['nickname']}")
    print(f"   Prompts: {len(data['prompts'])} prompts")
    print(f"   Target LLM: {data['target_llm']}")
    print("   📊 Score results will be included in the Slack notification!")
    print()
    
    try:
        # Send the request
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request submitted successfully!")
            print(f"   Job ID: {result.get('job_id', 'N/A')}")
            print(f"   Status: {result.get('status', 'N/A')}")
            print()
            
            if slack_webhook:
                print("📱 You should receive a Slack notification when the analysis completes.")
            else:
                print("📱 Slack notifications are disabled (SLACK_WEBHOOK_URL not set).")
            
            print("\n💡 You can also check the job status by querying the database or checking the logs.")
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API server.")
        print("   Make sure the server is running:")
        print("   uvicorn rainbowplus.api.app:app --host 0.0.0.0 --port 8000")
    except Exception as e:
        print(f"❌ An error occurred: {str(e)}")


if __name__ == "__main__":
    test_rainbowplus_with_slack()
