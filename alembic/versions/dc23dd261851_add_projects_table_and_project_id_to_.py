"""Add projects table and project_id to rainbow_analysis_jobs

Revision ID: dc23dd261851
Revises: 22c44a5cf15f
Create Date: 2025-07-13 19:03:04.270911

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dc23dd261851'
down_revision: Union[str, Sequence[str], None] = '22c44a5cf15f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create projects table
    op.create_table('projects',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('domain', sa.String(length=100), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Add project_id column to rainbow_analysis_jobs
    op.add_column('rainbow_analysis_jobs', sa.Column('project_id', sa.UUID(), nullable=True))

    # Add foreign key constraint
    op.create_foreign_key(
        'fk_rainbow_analysis_jobs_project_id',
        'rainbow_analysis_jobs', 'projects',
        ['project_id'], ['id']
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop foreign key constraint
    op.drop_constraint('fk_rainbow_analysis_jobs_project_id', 'rainbow_analysis_jobs', type_='foreignkey')

    # Drop project_id column
    op.drop_column('rainbow_analysis_jobs', 'project_id')

    # Drop projects table
    op.drop_table('projects')
