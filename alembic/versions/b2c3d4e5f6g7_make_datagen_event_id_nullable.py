"""Make datagen_event_id nullable in generated_datasets table

Revision ID: b2c3d4e5f6g7
Revises: a1b2c3d4e5f6
Create Date: 2025-07-16 12:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'b2c3d4e5f6g7'
down_revision: Union[str, Sequence[str], None] = 'a1b2c3d4e5f6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Make datagen_event_id nullable
    op.alter_column('generated_datasets', 'datagen_event_id',
                    existing_type=sa.UUID(),
                    nullable=True)


def downgrade() -> None:
    """Downgrade schema."""
    # Make datagen_event_id not nullable (this may fail if there are null values)
    op.alter_column('generated_datasets', 'datagen_event_id',
                    existing_type=sa.UUID(),
                    nullable=False)
