"""Add structured tables for adversarial prompts, responses, and scores

Revision ID: 22c44a5cf15f
Revises: 
Create Date: 2025-07-10 06:42:58.956728

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '22c44a5cf15f'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rainbow_seed_prompts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=False),
    sa.Column('prompt_text', sa.Text(), nullable=False),
    sa.Column('prompt_index', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['rainbow_analysis_jobs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_seed_prompt_job_id', 'rainbow_seed_prompts', ['job_id'], unique=False)
    op.create_index('idx_seed_prompt_job_index', 'rainbow_seed_prompts', ['job_id', 'prompt_index'], unique=False)
    op.create_table('rainbow_adversarial_prompts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=False),
    sa.Column('prompt_text', sa.Text(), nullable=False),
    sa.Column('descriptor', sa.String(length=500), nullable=False),
    sa.Column('descriptor_key', sa.String(length=500), nullable=True),
    sa.Column('iteration', sa.Integer(), nullable=False),
    sa.Column('mutation_index', sa.Integer(), nullable=True),
    sa.Column('seed_prompt_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['rainbow_analysis_jobs.id'], ),
    sa.ForeignKeyConstraint(['seed_prompt_id'], ['rainbow_seed_prompts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_adv_prompt_descriptor', 'rainbow_adversarial_prompts', ['job_id', 'descriptor'], unique=False)
    op.create_index('idx_adv_prompt_iteration', 'rainbow_adversarial_prompts', ['job_id', 'iteration'], unique=False)
    op.create_index('idx_adv_prompt_job_id', 'rainbow_adversarial_prompts', ['job_id'], unique=False)
    op.create_index('idx_adv_prompt_seed', 'rainbow_adversarial_prompts', ['seed_prompt_id'], unique=False)
    op.create_table('rainbow_model_responses',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=False),
    sa.Column('adversarial_prompt_id', sa.UUID(), nullable=False),
    sa.Column('response_text', sa.Text(), nullable=False),
    sa.Column('model_name', sa.String(length=255), nullable=True),
    sa.Column('model_base_url', sa.Text(), nullable=True),
    sa.Column('response_index', sa.Integer(), nullable=True),
    sa.Column('generation_time_ms', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['adversarial_prompt_id'], ['rainbow_adversarial_prompts.id'], ),
    sa.ForeignKeyConstraint(['job_id'], ['rainbow_analysis_jobs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_response_job_id', 'rainbow_model_responses', ['job_id'], unique=False)
    op.create_index('idx_response_model', 'rainbow_model_responses', ['job_id', 'model_name'], unique=False)
    op.create_index('idx_response_prompt_id', 'rainbow_model_responses', ['adversarial_prompt_id'], unique=False)
    op.create_table('rainbow_prompt_scores',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=False),
    sa.Column('adversarial_prompt_id', sa.UUID(), nullable=False),
    sa.Column('score_value', sa.Float(), nullable=False),
    sa.Column('score_type', sa.String(length=100), nullable=False),
    sa.Column('scorer_name', sa.String(length=255), nullable=True),
    sa.Column('score_details', sa.JSON(), nullable=True),
    sa.Column('threshold_passed', sa.String(length=10), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['adversarial_prompt_id'], ['rainbow_adversarial_prompts.id'], ),
    sa.ForeignKeyConstraint(['job_id'], ['rainbow_analysis_jobs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_score_job_id', 'rainbow_prompt_scores', ['job_id'], unique=False)
    op.create_index('idx_score_prompt_id', 'rainbow_prompt_scores', ['adversarial_prompt_id'], unique=False)
    op.create_index('idx_score_threshold', 'rainbow_prompt_scores', ['job_id', 'threshold_passed'], unique=False)
    op.create_index('idx_score_type', 'rainbow_prompt_scores', ['job_id', 'score_type'], unique=False)
    op.create_index('idx_score_value', 'rainbow_prompt_scores', ['job_id', 'score_value'], unique=False)
    op.drop_table('task_runs')
    op.drop_index(op.f('idx_task_prompts_iteration'), table_name='task_prompts')
    op.drop_index(op.f('idx_task_prompts_parent'), table_name='task_prompts')
    op.drop_index(op.f('idx_task_prompts_task_run_type'), table_name='task_prompts')
    op.drop_table('task_prompts')
    op.drop_table('task_analysis_reports')
    op.drop_index(op.f('idx_task_scores_fitness'), table_name='task_scores')
    op.drop_index(op.f('idx_task_scores_prompt_response'), table_name='task_scores')
    op.drop_table('task_scores')
    op.drop_index(op.f('idx_task_iterations_task_run'), table_name='task_iterations')
    op.drop_table('task_iterations')
    op.drop_index(op.f('idx_task_responses_prompt'), table_name='task_responses')
    op.drop_table('task_responses')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('task_responses',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('task_run_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('prompt_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('response_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('target_prompt', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('model_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['prompt_id'], ['task_prompts.id'], name='task_responses_prompt_id_fkey'),
    sa.ForeignKeyConstraint(['task_run_id'], ['task_runs.id'], name='task_responses_task_run_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='task_responses_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('idx_task_responses_prompt'), 'task_responses', ['prompt_id'], unique=False)
    op.create_table('task_iterations',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('task_run_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('iteration_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('selected_prompt_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('descriptor_key', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('descriptor_values', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('num_mutations_generated', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('num_mutations_filtered', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('num_responses_generated', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('success', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['task_run_id'], ['task_runs.id'], name=op.f('task_iterations_task_run_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('task_iterations_pkey'))
    )
    op.create_index(op.f('idx_task_iterations_task_run'), 'task_iterations', ['task_run_id', 'iteration_id'], unique=False)
    op.create_table('task_scores',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('task_run_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('prompt_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('response_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('score_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('fitness_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('score_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('scorer_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['prompt_id'], ['task_prompts.id'], name=op.f('task_scores_prompt_id_fkey')),
    sa.ForeignKeyConstraint(['response_id'], ['task_responses.id'], name=op.f('task_scores_response_id_fkey')),
    sa.ForeignKeyConstraint(['task_run_id'], ['task_runs.id'], name=op.f('task_scores_task_run_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('task_scores_pkey'))
    )
    op.create_index(op.f('idx_task_scores_prompt_response'), 'task_scores', ['prompt_id', 'response_id'], unique=False)
    op.create_index(op.f('idx_task_scores_fitness'), 'task_scores', ['task_run_id', sa.literal_column('fitness_score DESC')], unique=False)
    op.create_table('task_analysis_reports',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('task_run_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('report_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('report_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['task_run_id'], ['task_runs.id'], name=op.f('task_analysis_reports_task_run_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('task_analysis_reports_pkey'))
    )
    op.create_table('task_prompts',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('task_run_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('prompt_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('prompt_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('parent_prompt_id', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('iteration', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('descriptor_key', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('descriptor_values', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('mutator_prompt', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('similarity_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['task_run_id'], ['task_runs.id'], name=op.f('task_prompts_task_run_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('task_prompts_pkey'))
    )
    op.create_index(op.f('idx_task_prompts_task_run_type'), 'task_prompts', ['task_run_id', 'prompt_type'], unique=False)
    op.create_index(op.f('idx_task_prompts_parent'), 'task_prompts', ['task_run_id', 'parent_prompt_id'], unique=False)
    op.create_index(op.f('idx_task_prompts_iteration'), 'task_prompts', ['task_run_id', 'iteration'], unique=False)
    op.create_table('task_runs',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('task_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('config_file', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('target_llm_model', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('mutator_llm_model', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('fitness_llm_model', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('max_iterations', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('num_samples', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('num_mutations', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('similarity_threshold', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('fitness_threshold', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('completed_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('task_runs_pkey'))
    )
    op.drop_index('idx_score_value', table_name='rainbow_prompt_scores')
    op.drop_index('idx_score_type', table_name='rainbow_prompt_scores')
    op.drop_index('idx_score_threshold', table_name='rainbow_prompt_scores')
    op.drop_index('idx_score_prompt_id', table_name='rainbow_prompt_scores')
    op.drop_index('idx_score_job_id', table_name='rainbow_prompt_scores')
    op.drop_table('rainbow_prompt_scores')
    op.drop_index('idx_response_prompt_id', table_name='rainbow_model_responses')
    op.drop_index('idx_response_model', table_name='rainbow_model_responses')
    op.drop_index('idx_response_job_id', table_name='rainbow_model_responses')
    op.drop_table('rainbow_model_responses')
    op.drop_index('idx_adv_prompt_seed', table_name='rainbow_adversarial_prompts')
    op.drop_index('idx_adv_prompt_job_id', table_name='rainbow_adversarial_prompts')
    op.drop_index('idx_adv_prompt_iteration', table_name='rainbow_adversarial_prompts')
    op.drop_index('idx_adv_prompt_descriptor', table_name='rainbow_adversarial_prompts')
    op.drop_table('rainbow_adversarial_prompts')
    op.drop_index('idx_seed_prompt_job_index', table_name='rainbow_seed_prompts')
    op.drop_index('idx_seed_prompt_job_id', table_name='rainbow_seed_prompts')
    op.drop_table('rainbow_seed_prompts')
    # ### end Alembic commands ###
