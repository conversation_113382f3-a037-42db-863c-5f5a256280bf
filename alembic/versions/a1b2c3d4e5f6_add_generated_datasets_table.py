"""Add generated_datasets table for storing dataset generation results

Revision ID: a1b2c3d4e5f6
Revises: dc23dd261851
Create Date: 2025-07-16 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a1b2c3d4e5f6'
down_revision: Union[str, Sequence[str], None] = 'dc23dd261851'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create generated_datasets table
    op.create_table('generated_datasets',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('datagen_event_id', sa.UUID(), nullable=False),
        sa.Column('project_id', sa.UUID(), nullable=True),
        sa.Column('job_id', sa.UUID(), nullable=True),
        sa.Column('dataset_name', sa.String(length=255), nullable=True),
        sa.Column('generation_status', sa.String(length=50), nullable=False, server_default='pending'),
        sa.Column('dataset_content', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('dataset_size', sa.Integer(), nullable=True),
        sa.Column('complexity', sa.Integer(), nullable=True),
        sa.Column('coverage', sa.Integer(), nullable=True),
        sa.Column('total_samples', sa.Integer(), nullable=True),
        sa.Column('sample_groups', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add foreign key constraints
    op.create_foreign_key(
        'fk_generated_datasets_datagen_event_id',
        'generated_datasets', 'datagen_events',
        ['datagen_event_id'], ['id']
    )
    
    op.create_foreign_key(
        'fk_generated_datasets_project_id',
        'generated_datasets', 'projects',
        ['project_id'], ['id']
    )
    
    op.create_foreign_key(
        'fk_generated_datasets_job_id',
        'generated_datasets', 'rainbow_analysis_jobs',
        ['job_id'], ['id']
    )
    
    # Create indexes for better query performance
    op.create_index('idx_generated_datasets_datagen_event_id', 'generated_datasets', ['datagen_event_id'])
    op.create_index('idx_generated_datasets_project_id', 'generated_datasets', ['project_id'])
    op.create_index('idx_generated_datasets_job_id', 'generated_datasets', ['job_id'])
    op.create_index('idx_generated_datasets_status', 'generated_datasets', ['generation_status'])
    op.create_index('idx_generated_datasets_created_at', 'generated_datasets', ['created_at'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes
    op.drop_index('idx_generated_datasets_created_at', table_name='generated_datasets')
    op.drop_index('idx_generated_datasets_status', table_name='generated_datasets')
    op.drop_index('idx_generated_datasets_job_id', table_name='generated_datasets')
    op.drop_index('idx_generated_datasets_project_id', table_name='generated_datasets')
    op.drop_index('idx_generated_datasets_datagen_event_id', table_name='generated_datasets')
    
    # Drop foreign key constraints
    op.drop_constraint('fk_generated_datasets_job_id', 'generated_datasets', type_='foreignkey')
    op.drop_constraint('fk_generated_datasets_project_id', 'generated_datasets', type_='foreignkey')
    op.drop_constraint('fk_generated_datasets_datagen_event_id', 'generated_datasets', type_='foreignkey')
    
    # Drop table
    op.drop_table('generated_datasets')
