#!/usr/bin/env python3
"""
Test script for dataset generation API to verify the new GeneratedDataset table functionality.
"""

import requests
import json
import time

# API endpoints
BASE_URL = "http://localhost:8000"
DATAGEN_EVENT_URL = f"{BASE_URL}/datagen/events"
DATASET_GENERATION_URL = f"{BASE_URL}/datasets/generate"

def test_dataset_generation():
    """Test the complete dataset generation workflow."""
    
    print("🧪 Testing Dataset Generation with new GeneratedDataset table")
    print("=" * 60)
    
    # Step 1: Create a datagen event (simulating form submission)
    print("📝 Step 1: Creating datagen event...")
    
    datagen_event_data = {
        "application_description": "Test application for incident reporting",
        "domain": "Public Safety",
        "example_input": "Report suspicious activity in the park",
        "test_types": [
            {"id": "harassment", "label": "Harassment", "checked": True},
            {"id": "cheating", "label": "Cheating", "checked": True}
        ],
        "complexity": 5,
        "coverage": 70,
        "dataset_size": 100,
        "selected_experts": ["safety_expert", "legal_expert"],
        "uploaded_files": ["test_file.txt"],
        "user_session_id": "test-session-123",
        "project_id": None  # No project for this test
    }
    
    try:
        event_response = requests.post(DATAGEN_EVENT_URL, json=datagen_event_data)
        event_response.raise_for_status()
        event_result = event_response.json()
        print(f"✅ Datagen event created: {event_result}")
        event_id = event_result.get('event_id')
    except Exception as e:
        print(f"❌ Failed to create datagen event: {e}")
        return False
    
    # Step 2: Generate dataset
    print("\n🚀 Step 2: Generating dataset...")
    
    dataset_generation_data = {
        "description": "Test application for incident reporting",
        "examples": ["Report suspicious activity in the park"],
        "task": "Content",
        "llm": {
            "provider": "openai",
            "api_key": "test-key",  # This will use fallback/mock
            "model_kwargs": {
                "model": "gpt-4o-mini"
            },
            "sampling_params": {
                "temperature": 0.7,
                "max_tokens": 2048,
                "top_p": 0.9,
                "n": 1
            }
        },
        "nickname": "test-user",
        "project_id": None
    }
    
    try:
        dataset_response = requests.post(DATASET_GENERATION_URL, json=dataset_generation_data)
        dataset_response.raise_for_status()
        dataset_result = dataset_response.json()
        print(f"✅ Dataset generation started: {dataset_result}")
        job_id = dataset_result.get('job_id')
    except Exception as e:
        print(f"❌ Failed to start dataset generation: {e}")
        return False
    
    # Step 3: Wait a bit for the generation to complete (it runs async)
    print(f"\n⏳ Step 3: Waiting for dataset generation to complete (job_id: {job_id})...")
    time.sleep(5)  # Give it some time to process
    
    # Step 4: Check if data was saved to the new GeneratedDataset table
    print("\n🔍 Step 4: Checking database for generated dataset...")
    
    # We'll need to query the database directly since there's no API endpoint yet
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import GeneratedDataset, RainbowAnalysisJob
        
        with get_db_session() as session:
            # Check if the job exists and is completed
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
            if job:
                print(f"📊 Job status: {job.status}")
                print(f"📊 Job created: {job.created_at}")
                
                # Check if GeneratedDataset was created
                generated_dataset = session.query(GeneratedDataset).filter(GeneratedDataset.job_id == job_id).first()
                if generated_dataset:
                    print(f"✅ GeneratedDataset found!")
                    print(f"   ID: {generated_dataset.id}")
                    print(f"   Status: {generated_dataset.generation_status}")
                    print(f"   Dataset name: {generated_dataset.dataset_name}")
                    print(f"   Total samples: {generated_dataset.total_samples}")
                    print(f"   Sample groups: {generated_dataset.sample_groups}")
                    print(f"   Created: {generated_dataset.created_at}")
                    print(f"   Completed: {generated_dataset.completed_at}")
                    
                    # Check if dataset content exists
                    if generated_dataset.dataset_content:
                        print(f"   Dataset content size: {len(str(generated_dataset.dataset_content))} characters")
                        print(f"   Content preview: {str(generated_dataset.dataset_content)[:200]}...")
                    else:
                        print("   ⚠️  No dataset content found")
                    
                    return True
                else:
                    print("❌ No GeneratedDataset found for this job")
                    return False
            else:
                print(f"❌ Job {job_id} not found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 RainbowPlus Dataset Generation Test")
    print("Testing new GeneratedDataset table functionality")
    print()
    
    success = test_dataset_generation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test completed successfully!")
        print("✅ Dataset generation saves to new GeneratedDataset table")
    else:
        print("❌ Test failed!")
        print("💡 Check the logs above for details")
    
    return success

if __name__ == "__main__":
    main()
