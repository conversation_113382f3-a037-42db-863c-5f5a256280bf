#!/mnt/gudga1/ai/knsoft/rainbowplus/rbp2/bin/python
#
# Author: <PERSON> (mmckerns @caltech and @uqfoundation)
# Copyright (c) 2008-2016 California Institute of Technology.
# Copyright (c) 2016-2024 The Uncertainty Quantification Foundation.
# License: 3-clause BSD.  The full license text is available at:
#  - https://github.com/uqfoundation/dill/blob/master/LICENSE
"""
display the reference paths for objects in ``dill.types`` or a .pkl file

Notes:
    the generated image is useful in showing the pointer references in
    objects that are or can be pickled.  Any object in ``dill.objects``
    listed in ``dill.load_types(picklable=True, unpicklable=True)`` works.

Examples::

    $ get_objgraph ArrayType
    Image generated as ArrayType.png
"""

import dill as pickle
#pickle.debug.trace(True)
#import pickle

# get all objects for testing
from dill import load_types
load_types(pickleable=True,unpickleable=True)
from dill import objects

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print ("Please provide exactly one file or type name (e.g. 'IntType')")
        msg = "\n"
        for objtype in list(objects.keys())[:40]:
            msg += objtype + ', '
        print (msg + "...")
    else:
        objtype = str(sys.argv[-1])
        try:
            obj = objects[objtype]
        except KeyError:
            obj = pickle.load(open(objtype,'rb'))
            import os
            objtype = os.path.splitext(objtype)[0]
        try:
            import objgraph
            objgraph.show_refs(obj, filename=objtype+'.png')
        except ImportError:
            print ("Please install 'objgraph' to view object graphs")


# EOF
