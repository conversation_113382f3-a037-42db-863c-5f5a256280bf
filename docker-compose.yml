version: '3.8'
services:
  # web:
  #   image: rainbowplus:latest
  #   # build:
  #   #   context: .
  #   #   dockerfile: Dockerfile
  #   ports:
  #     - "80:80"
  #   environment:
  #     - REDIS_URL=redis://redis:6379/0
  #     - KEY_VAULT_URL=https://<vault-name>.vault.azure.net
  #     - THIRD_PARTY_API_KEY=<your-openai-api-key>  # Temporary for testing
  #   depends_on:
  #     - redis
  #     - worker
  #   # volumes:
  #   #   - ./logs:/code/logs
  #   #   - ./logs-sota:/code/logs-sota
  # worker:
  #   image: rainbowplus:latest
  #   command: celery -A rainbowplus.tasks.celery worker --loglevel=info
  #   environment:
  #     - REDIS_URL=redis://redis:6379/0
  #     - KEY_VAULT_URL=https://<vault-name>.vault.azure.net
  #     - THIRD_PARTY_API_KEY=<your-openai-api-key>  # Temporary for testing
  #   depends_on:
  #     - redis
  #   # volumes:
  #   #   - ./logs:/code/logs
  #   #   - ./logs-sota:/code/logs-sota
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: mydb
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U myuser -d mydb"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
