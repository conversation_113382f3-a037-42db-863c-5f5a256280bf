{"healthChecks": {"basic": {"path": "/health", "intervalSeconds": 30, "timeoutSeconds": 5, "unhealthyThreshold": 3, "healthyThreshold": 2, "description": "Basic health check for Azure Container Apps"}, "detailed": {"path": "/health/detailed", "intervalSeconds": 60, "timeoutSeconds": 10, "unhealthyThreshold": 3, "healthyThreshold": 2, "description": "Detailed health check including all dependencies"}, "database": {"path": "/health/database", "intervalSeconds": 45, "timeoutSeconds": 8, "unhealthyThreshold": 2, "healthyThreshold": 1, "description": "PostgreSQL database health check"}, "celery": {"path": "/health/celery", "intervalSeconds": 60, "timeoutSeconds": 10, "unhealthyThreshold": 3, "healthyThreshold": 2, "description": "Celery worker health check"}}, "azureContainerApps": {"livenessProbe": {"httpGet": {"path": "/health", "port": 8000, "scheme": "HTTP"}, "initialDelaySeconds": 30, "periodSeconds": 30, "timeoutSeconds": 5, "successThreshold": 1, "failureThreshold": 3}, "readinessProbe": {"httpGet": {"path": "/health/detailed", "port": 8000, "scheme": "HTTP"}, "initialDelaySeconds": 10, "periodSeconds": 10, "timeoutSeconds": 10, "successThreshold": 1, "failureThreshold": 3}}, "monitoring": {"alerts": {"unhealthyService": {"condition": "health_status != 'healthy'", "severity": "critical", "description": "RainbowPlus service is unhealthy"}, "databaseDown": {"condition": "database_status != 'healthy'", "severity": "critical", "description": "PostgreSQL database is down"}, "celeryWorkersDown": {"condition": "celery_worker_count == 0", "severity": "high", "description": "No Celery workers are running"}, "highMemoryUsage": {"condition": "memory_percent > 85", "severity": "warning", "description": "High memory usage detected"}, "highCpuUsage": {"condition": "cpu_percent > 80", "severity": "warning", "description": "High CPU usage detected"}}}}