#!/bin/bash

# Shell script to run the pipeline database output test with proper PYTHONPATH and module path

echo "Running pipeline database output test..."

export PYTHONPATH=$(pwd)

python3 -m unittest discover -s tests -p "test_pipeline_database_output.py"

TEST_RESULT=$?

if [ $TEST_RESULT -eq 0 ]; then
  echo "Test passed successfully."
else
  echo "Test failed. Inspect the output above for errors."
fi

exit $TEST_RESULT