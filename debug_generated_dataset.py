#!/usr/bin/env python3
"""
Debug the GeneratedDataset record to see why individual samples weren't created.
"""

def debug_generated_dataset():
    """Debug the GeneratedDataset record."""
    
    print("🔍 Debugging GeneratedDataset Record")
    print("=" * 50)
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import GeneratedDataset, DatasetSample
        import json
        
        with get_db_session() as session:
            # Get the GeneratedDataset record
            dataset = session.query(GeneratedDataset).first()
            
            if not dataset:
                print("❌ No GeneratedDataset found")
                return
            
            print(f"📊 GeneratedDataset ID: {dataset.id}")
            print(f"📊 Job ID: {dataset.job_id}")
            print(f"📊 Status: {dataset.generation_status}")
            print(f"📊 Total samples: {dataset.total_samples}")
            print(f"📊 Sample groups: {dataset.sample_groups}")
            print(f"📊 Created: {dataset.created_at}")
            
            # Check the dataset_content structure
            print(f"\n📊 Dataset content type: {type(dataset.dataset_content)}")
            
            if dataset.dataset_content:
                if isinstance(dataset.dataset_content, list):
                    print(f"📊 Dataset content is a list with {len(dataset.dataset_content)} items")
                    
                    for i, item in enumerate(dataset.dataset_content[:2]):  # Show first 2 items
                        print(f"\n   Item {i+1}:")
                        print(f"     Type: {type(item)}")
                        if isinstance(item, dict):
                            print(f"     Keys: {list(item.keys())}")
                            if 'samples' in item:
                                samples = item['samples']
                                print(f"     Samples count: {len(samples) if isinstance(samples, list) else 'Not a list'}")
                                if isinstance(samples, list) and len(samples) > 0:
                                    first_sample = samples[0]
                                    print(f"     First sample type: {type(first_sample)}")
                                    if isinstance(first_sample, dict):
                                        print(f"     First sample keys: {list(first_sample.keys())}")
                                        print(f"     First sample content preview: {str(first_sample.get('content', ''))[:100]}...")
                elif isinstance(dataset.dataset_content, dict):
                    print(f"📊 Dataset content is a dict with keys: {list(dataset.dataset_content.keys())}")
                else:
                    print(f"📊 Dataset content: {str(dataset.dataset_content)[:200]}...")
            
            # Check if there are any individual samples for this dataset
            individual_samples = session.query(DatasetSample).filter(
                DatasetSample.generated_dataset_id == dataset.id
            ).all()
            
            print(f"\n📊 Individual samples for this dataset: {len(individual_samples)}")
            
            # Let's try to manually process this dataset to create individual samples
            if len(individual_samples) == 0 and dataset.dataset_content:
                print("\n🔧 Attempting to manually create individual samples...")
                
                from rainbowplus.api.models import DatasetSample
                import uuid
                
                sample_index = 0
                if isinstance(dataset.dataset_content, list):
                    for group_index, group in enumerate(dataset.dataset_content):
                        if isinstance(group, dict) and 'samples' in group:
                            for sample in group['samples']:
                                if isinstance(sample, dict):
                                    # Create individual DatasetSample record
                                    dataset_sample = DatasetSample(
                                        id=uuid.uuid4(),
                                        generated_dataset_id=dataset.id,
                                        datagen_event_id=dataset.datagen_event_id,
                                        project_id=dataset.project_id,
                                        job_id=dataset.job_id,
                                        sample_type=sample.get('type', 'content'),
                                        content=sample.get('content', ''),
                                        label=sample.get('label', None),
                                        sample_group=group_index,
                                        sample_index=sample_index
                                    )
                                    session.add(dataset_sample)
                                    sample_index += 1
                                    
                                    if sample_index <= 3:  # Show first 3
                                        print(f"   Created sample {sample_index}: {sample.get('label', 'No label')}")
                
                session.commit()
                print(f"✅ Created {sample_index} individual samples")
                
    except Exception as e:
        print(f"❌ Error debugging dataset: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_generated_dataset()
