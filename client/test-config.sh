#!/bin/bash

# Test script to verify the configuration system works
echo "🧪 Testing RainbowPlus Frontend Configuration System"
echo "=================================================="

# Test 1: Check if .env.local exists and has the right format
echo ""
echo "📋 Test 1: Environment Configuration"
if [ -f ".env.local" ]; then
    echo "✅ .env.local exists"
    
    if grep -q "NEXT_PUBLIC_API_URL=" .env.local; then
        API_URL=$(grep "NEXT_PUBLIC_API_URL=" .env.local | cut -d'=' -f2)
        echo "✅ API URL configured: $API_URL"
    else
        echo "❌ NEXT_PUBLIC_API_URL not found in .env.local"
    fi
else
    echo "⚠️  .env.local not found, will use defaults"
fi

# Test 2: Check if configuration files exist
echo ""
echo "📋 Test 2: Configuration Files"

files=(
    "lib/config.ts"
    "lib/api-client.ts"
    ".env.example"
    "scripts/configure-backend.js"
    "CONFIG.md"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file missing"
    fi
done

# Test 3: Check Next.js configuration
echo ""
echo "📋 Test 3: Next.js Configuration"
if [ -f "next.config.mjs" ]; then
    echo "✅ next.config.mjs exists"
    
    if grep -q "NEXT_PUBLIC_API_URL" next.config.mjs; then
        echo "✅ Next.js configured to use environment variables"
    else
        echo "❌ Next.js not configured for environment variables"
    fi
    
    if grep -q "rewrites" next.config.mjs; then
        echo "✅ API proxy configured"
    else
        echo "❌ API proxy not configured"
    fi
else
    echo "❌ next.config.mjs missing"
fi

# Test 4: Show current configuration
echo ""
echo "📋 Test 4: Current Configuration Summary"
echo "Current backend URL: ${API_URL:-'Not configured (will use defaults)'}"

if [ -n "$API_URL" ]; then
    if [[ "$API_URL" == *"localhost"* ]]; then
        echo "🏠 Configuration: Local development"
    elif [[ "$API_URL" == *"192.168"* ]] || [[ "$API_URL" == *"10."* ]]; then
        echo "🏠 Configuration: Local network"
    elif [[ "$API_URL" == *"staging"* ]]; then
        echo "🚧 Configuration: Staging environment"
    elif [[ "$API_URL" == *"https"* ]]; then
        echo "🌐 Configuration: Production environment"
    else
        echo "🔧 Configuration: Custom"
    fi
fi

# Test 5: Configuration change examples
echo ""
echo "📋 Test 5: How to Change Configuration"
echo ""
echo "To change the backend URL, you can:"
echo ""
echo "1. Use the configuration script (when Node.js is available):"
echo "   node scripts/configure-backend.js http://*************:8000"
echo ""
echo "2. Manually edit .env.local:"
echo "   echo 'NEXT_PUBLIC_API_URL=http://*************:8000' >> .env.local"
echo ""
echo "3. Use environment presets:"
echo "   node scripts/configure-backend.js local      # http://localhost:8000"
echo "   node scripts/configure-backend.js staging    # staging server"
echo "   node scripts/configure-backend.js production # production server"
echo ""
echo "After changing configuration, restart the development server:"
echo "   ./run-dev.sh"

echo ""
echo "🎉 Configuration test completed!"
