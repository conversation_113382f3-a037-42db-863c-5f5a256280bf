#!/bin/bash

# Simple shell script to configure backend URL
# Alternative to the Node.js script for environments where Node.js is not available

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Presets
declare -A PRESETS=(
    ["local"]="http://localhost:8000"
    ["local-ip"]="http://*************:8000"
    ["staging"]="https://staging-api.rainbowplus.com"
    ["production"]="https://api.rainbowplus.com"
)

show_usage() {
    echo -e "${BLUE}🔧 RainbowPlus Backend Configuration Tool${NC}"
    echo "=========================================="
    echo ""
    echo "Usage:"
    echo "  ./configure-backend.sh [backend-url]"
    echo "  ./configure-backend.sh [preset]"
    echo ""
    echo "Examples:"
    echo "  ./configure-backend.sh http://*************:8000"
    echo "  ./configure-backend.sh local"
    echo "  ./configure-backend.sh staging"
    echo ""
    echo "Available presets:"
    for preset in "${!PRESETS[@]}"; do
        printf "  %-12s -> %s\n" "$preset" "${PRESETS[$preset]}"
    done
    echo ""
    echo "Options:"
    echo "  --current, -c    Show current configuration"
    echo "  --help, -h       Show this help message"
    echo ""
    show_current_config
}

show_current_config() {
    echo "Current configuration:"
    if [ -f ".env.local" ] && grep -q "NEXT_PUBLIC_API_URL=" .env.local; then
        local current_url=$(grep "NEXT_PUBLIC_API_URL=" .env.local | cut -d'=' -f2)
        echo -e "  Current backend: ${GREEN}$current_url${NC}"
    else
        echo -e "  Current backend: ${YELLOW}Not configured (using defaults)${NC}"
    fi
}

validate_url() {
    local url="$1"
    
    # Basic URL validation
    if [[ "$url" =~ ^https?://[a-zA-Z0-9.-]+(:[0-9]+)?(/.*)?$ ]]; then
        return 0
    else
        return 1
    fi
}

update_env_file() {
    local backend_url="$1"
    local env_file=".env.local"
    
    # Create .env.local from .env.example if it doesn't exist
    if [ ! -f "$env_file" ]; then
        if [ -f ".env.example" ]; then
            echo -e "${BLUE}📄 Creating .env.local from .env.example${NC}"
            cp .env.example "$env_file"
        else
            echo -e "${BLUE}📄 Creating new .env.local${NC}"
            cat > "$env_file" << EOF
# Local development configuration
# Generated by configure-backend.sh

NODE_ENV=development
PORT=3001
NEXT_PUBLIC_API_TIMEOUT=30000
NEXT_PUBLIC_APP_VERSION=1.0.0
EOF
        fi
    fi
    
    # Update or add NEXT_PUBLIC_API_URL
    if grep -q "NEXT_PUBLIC_API_URL=" "$env_file"; then
        # Update existing line
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=$backend_url|" "$env_file"
        else
            # Linux
            sed -i "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=$backend_url|" "$env_file"
        fi
    else
        # Add new line
        echo "NEXT_PUBLIC_API_URL=$backend_url" >> "$env_file"
    fi
    
    echo -e "${GREEN}✅ Backend URL updated to: $backend_url${NC}"
    echo -e "${BLUE}📁 Configuration saved to: $env_file${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  Remember to restart the development server:${NC}"
    echo "   ./run-dev.sh"
}

main() {
    local input="$1"
    
    # Handle special flags
    case "$input" in
        "--help"|"-h"|"")
            show_usage
            exit 0
            ;;
        "--current"|"-c")
            echo -e "${BLUE}🔧 Current Backend Configuration${NC}"
            echo "================================"
            show_current_config
            exit 0
            ;;
    esac
    
    # Check if input is a preset
    if [[ -n "${PRESETS[$input]}" ]]; then
        local backend_url="${PRESETS[$input]}"
        echo -e "${BLUE}🎯 Using preset '$input': $backend_url${NC}"
        update_env_file "$backend_url"
        return
    fi
    
    # Check if input is a valid URL
    if validate_url "$input"; then
        update_env_file "$input"
        return
    fi
    
    # Invalid input
    echo -e "${RED}❌ Invalid backend URL or preset: $input${NC}"
    echo ""
    show_usage
    exit 1
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "next.config.mjs" ]; then
    echo -e "${RED}❌ Error: This script must be run from the frontend client directory${NC}"
    echo "Make sure you're in the directory containing package.json and next.config.mjs"
    exit 1
fi

main "$@"
