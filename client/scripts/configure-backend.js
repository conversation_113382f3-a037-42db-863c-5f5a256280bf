#!/usr/bin/env node
/**
 * Configuration script to easily set the backend API URL
 * Usage: node scripts/configure-backend.js [backend-url]
 */

const fs = require('fs');
const path = require('path');

// Default configurations for different environments
const presets = {
  local: 'http://localhost:8000',
  'local-ip': 'http://*************:8000', // Example local network IP
  staging: 'https://staging-api.rainbowplus.com',
  production: 'https://api.rainbowplus.com',
};

function showUsage() {
  console.log('🔧 RainbowPlus Backend Configuration Tool');
  console.log('==========================================');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/configure-backend.js [backend-url]');
  console.log('  node scripts/configure-backend.js [preset]');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/configure-backend.js http://*************:8000');
  console.log('  node scripts/configure-backend.js local');
  console.log('  node scripts/configure-backend.js staging');
  console.log('');
  console.log('Available presets:');
  Object.entries(presets).forEach(([name, url]) => {
    console.log(`  ${name.padEnd(12)} -> ${url}`);
  });
  console.log('');
  console.log('Current configuration:');
  showCurrentConfig();
}

function showCurrentConfig() {
  const envPath = path.join(__dirname, '..', '.env.local');
  
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const apiUrlMatch = envContent.match(/NEXT_PUBLIC_API_URL=(.+)/);
    
    if (apiUrlMatch) {
      console.log(`  Current backend: ${apiUrlMatch[1]}`);
    } else {
      console.log('  Current backend: Not configured');
    }
  } else {
    console.log('  Current backend: .env.local not found (using defaults)');
  }
}

function updateEnvFile(backendUrl) {
  const envPath = path.join(__dirname, '..', '.env.local');
  const envExamplePath = path.join(__dirname, '..', '.env.example');
  
  let envContent = '';
  
  // Read existing .env.local or create from .env.example
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  } else if (fs.existsSync(envExamplePath)) {
    envContent = fs.readFileSync(envExamplePath, 'utf8');
    console.log('📄 Creating .env.local from .env.example');
  } else {
    // Create minimal .env.local
    envContent = `# Local development configuration
# Generated by configure-backend.js

NODE_ENV=development
PORT=3001
NEXT_PUBLIC_API_TIMEOUT=30000
NEXT_PUBLIC_APP_VERSION=1.0.0
`;
  }
  
  // Update or add NEXT_PUBLIC_API_URL
  if (envContent.includes('NEXT_PUBLIC_API_URL=')) {
    envContent = envContent.replace(
      /NEXT_PUBLIC_API_URL=.+/,
      `NEXT_PUBLIC_API_URL=${backendUrl}`
    );
  } else {
    envContent += `\nNEXT_PUBLIC_API_URL=${backendUrl}\n`;
  }
  
  // Write the updated content
  fs.writeFileSync(envPath, envContent);
  
  console.log(`✅ Backend URL updated to: ${backendUrl}`);
  console.log(`📁 Configuration saved to: ${envPath}`);
}

function validateUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    showUsage();
    return;
  }
  
  const input = args[0];
  
  // Check if input is a preset
  if (presets[input]) {
    const backendUrl = presets[input];
    console.log(`🎯 Using preset '${input}': ${backendUrl}`);
    updateEnvFile(backendUrl);
    return;
  }
  
  // Check if input is a valid URL
  if (validateUrl(input)) {
    updateEnvFile(input);
    return;
  }
  
  // Invalid input
  console.error(`❌ Invalid backend URL or preset: ${input}`);
  console.error('');
  showUsage();
  process.exit(1);
}

// Handle special commands
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showUsage();
  process.exit(0);
}

if (process.argv.includes('--current') || process.argv.includes('-c')) {
  console.log('🔧 Current Backend Configuration');
  console.log('================================');
  showCurrentConfig();
  process.exit(0);
}

main();
