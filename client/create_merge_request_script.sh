
#!/bin/bash

# Check if required arguments are provided
if [ $# -ne 4 ]; then
  echo "Usage: $0 <source-branch> <new-branch> <project-id> <gitlab-token>"
  exit 1
fi

SOURCE_BRANCH=$1
NEW_BRANCH=$2
PROJECT_ID=$3
GITLAB_TOKEN=$4
GITLAB_API_URL="https://gitlab.com/api/v4"  # Change if using a self-hosted GitLab instance

# Verify that the current branch is the new branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ "$CURRENT_BRANCH" != "$NEW_BRANCH" ]; then
  echo "Error: Current branch is '$CURRENT_BRANCH', expected '$NEW_BRANCH'"
  exit 1
fi

# Ensure we're in a Git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
  echo "Error: Not inside a Git repository"
  exit 1
fi

# Pull latest changes from source branch to ensure no conflicts
echo "Pulling latest changes for '$SOURCE_BRANCH' from remote..."
git fetch origin "$SOURCE_BRANCH"

# Merge source branch into new branch
echo "Merging '$SOURCE_BRANCH' into '$NEW_BRANCH'..."
git merge "origin/$SOURCE_BRANCH" --no-edit

# Check if merge was successful
if [ $? -eq 0 ]; then
  echo "Merge successful!"
else
  echo "Merge failed. Please resolve conflicts manually."
  exit 1
fi

# Push the updated new branch to remote
echo "Pushing updated '$NEW_BRANCH' to remote..."
git push origin "$NEW_BRANCH"

# Create a merge request using GitLab API
echo "Creating merge request for '$NEW_BRANCH' targeting '$SOURCE_BRANCH'..."
MR_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" \
  --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
  --data "source_branch=$NEW_BRANCH&target_branch=$SOURCE_BRANCH&title=Automated MR: $NEW_BRANCH to $SOURCE_BRANCH&description=Automated merge request created by script" \
  "$GITLAB_API_URL/projects/$PROJECT_ID/merge_requests")

# Check if MR creation was successful
if [ "$MR_RESPONSE" -eq 201 ]; then
  echo "Merge request created successfully!"
else
  echo "Failed to create merge request. HTTP status: $MR_RESPONSE"
  exit 1
fi

echo "Finish complete! Branch '$NEW_BRANCH' updated and MR created."
