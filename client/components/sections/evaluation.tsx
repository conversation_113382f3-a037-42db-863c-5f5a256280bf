"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"

const evaluationData = [
  { name: "Accuracy", llmJudge: 87.5, domainExpert: 89.2, alignment: 94.1 },
  { name: "Safety", llmJudge: 92.1, domainExpert: 88.7, alignment: 96.2 },
  { name: "Relevance", llmJudge: 84.3, domainExpert: 87.6, alignment: 92.8 },
  { name: "Consistency", llmJudge: 91.2, domainExpert: 89.9, alignment: 98.5 },
  { name: "<PERSON>ias Detection", llmJudge: 76.8, domainExpert: 82.1, alignment: 89.4 },
]

const getAlignmentColor = (alignment: number) => {
  if (alignment > 95) return "default"
  if (alignment > 90) return "secondary"
  return "destructive"
}

export function Evaluation() {
  return (
    <div className="space-y-6">
      {/* Chart */}
      <Card>
        <CardHeader>
          <CardTitle>LLM Judge vs Domain Expert Alignment</CardTitle>
          <CardDescription>
            Comparison of evaluation metrics between automated LLM judges and human domain experts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={evaluationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="llmJudge" fill="hsl(var(--primary))" name="LLM Judge" />
                <Bar dataKey="domainExpert" fill="hsl(var(--secondary))" name="Domain Expert" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Table */}
      <Card>
        <CardHeader>
          <CardTitle>Evaluation Metrics</CardTitle>
          <CardDescription>Detailed comparison of evaluation scores and alignment percentages</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Metric</TableHead>
                <TableHead>LLM Judge</TableHead>
                <TableHead>Domain Expert</TableHead>
                <TableHead>Alignment %</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {evaluationData.map((metric) => (
                <TableRow key={metric.name}>
                  <TableCell className="font-medium">{metric.name}</TableCell>
                  <TableCell>{metric.llmJudge}%</TableCell>
                  <TableCell>{metric.domainExpert}%</TableCell>
                  <TableCell>
                    <Badge variant={getAlignmentColor(metric.alignment)}>{metric.alignment}%</Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex gap-4">
        <Button>Generate Report</Button>
        <Button variant="outline">Export Data</Button>
        <Button variant="outline">View Details</Button>
      </div>
    </div>
  )
}
