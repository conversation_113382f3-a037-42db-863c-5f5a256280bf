"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Play, Square, Calendar } from "lucide-react"

const attackVectors = [
  { id: "prompt-injection", name: "Prompt Injection", description: "Test resistance to malicious prompts" },
  { id: "jailbreak", name: "Jailbreak Attempts", description: "Try to bypass safety constraints" },
  { id: "data-extraction", name: "Data Extraction", description: "Attempt to extract training data" },
  { id: "model-poisoning", name: "Model Poisoning", description: "Test input manipulation attacks" },
  { id: "adversarial", name: "Adversarial Examples", description: "Use adversarial inputs to fool model" },
  { id: "bias", name: "Bias Amplification", description: "Test for biased outputs" },
]

export function AutomateTesting() {
  const [config, setConfig] = useState({
    endpointUrl: "",
    authType: "bearer",
    apiKey: "",
    rateLimit: 60,
  })
  const [selectedVectors, setSelectedVectors] = useState<string[]>([])
  const [testingStatus, setTestingStatus] = useState({
    status: "Ready",
    testsRun: 0,
    successRate: "-",
  })
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<
    Array<{
      attackType: string
      status: string
      responseTime: string
      result: string
    }>
  >([])

  const toggleVector = (vectorId: string) => {
    setSelectedVectors((prev) => (prev.includes(vectorId) ? prev.filter((id) => id !== vectorId) : [...prev, vectorId]))
  }

  const startTesting = () => {
    if (!config.endpointUrl || !config.apiKey) {
      alert("Please provide endpoint URL and API key")
      return
    }
    if (selectedVectors.length === 0) {
      alert("Please select at least one attack vector")
      return
    }

    setIsRunning(true)
    setTestingStatus({ status: "Running", testsRun: 0, successRate: "-" })
    setResults([])

    // Simulate testing
    let testsRun = 0
    const interval = setInterval(() => {
      testsRun++
      const attackTypes = ["Prompt Injection", "Jailbreak", "Data Extraction", "Model Poisoning"]
      const statuses = ["Success", "Failed", "Blocked"]
      const results_list = ["Blocked", "Detected", "Bypassed"]

      const newResult = {
        attackType: attackTypes[Math.floor(Math.random() * attackTypes.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        responseTime: `${Math.floor(Math.random() * 500) + 100}ms`,
        result: results_list[Math.floor(Math.random() * results_list.length)],
      }

      setResults((prev) => [newResult, ...prev.slice(0, 9)])
      setTestingStatus({
        status: "Running",
        testsRun,
        successRate: `${Math.floor(Math.random() * 20) + 80}%`,
      })

      if (testsRun >= 20) {
        clearInterval(interval)
        setIsRunning(false)
        setTestingStatus((prev) => ({ ...prev, status: "Completed" }))
      }
    }, 1000)
  }

  const stopTesting = () => {
    setIsRunning(false)
    setTestingStatus((prev) => ({ ...prev, status: "Stopped" }))
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Target Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Target Configuration</CardTitle>
            <CardDescription>Configure your LLM endpoint for testing</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="endpoint">Endpoint URL</Label>
              <Input
                id="endpoint"
                type="url"
                placeholder="https://api.example.com/llm"
                value={config.endpointUrl}
                onChange={(e) => setConfig({ ...config, endpointUrl: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="auth">Authentication</Label>
              <Select value={config.authType} onValueChange={(value) => setConfig({ ...config, authType: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bearer">Bearer Token</SelectItem>
                  <SelectItem value="api-key">API Key</SelectItem>
                  <SelectItem value="oauth">OAuth 2.0</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="api-key">API Key/Token</Label>
              <Input
                id="api-key"
                type="password"
                placeholder="Enter your API key"
                value={config.apiKey}
                onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="rate-limit">Rate Limit (requests/minute)</Label>
              <Input
                id="rate-limit"
                type="number"
                value={config.rateLimit}
                onChange={(e) => setConfig({ ...config, rateLimit: Number.parseInt(e.target.value) })}
              />
            </div>
          </CardContent>
        </Card>

        {/* Attack Vectors */}
        <Card>
          <CardHeader>
            <CardTitle>Attack Vectors</CardTitle>
            <CardDescription>Select the types of attacks to test</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-3">
              {attackVectors.map((vector) => (
                <Card
                  key={vector.id}
                  className={`cursor-pointer transition-colors ${
                    selectedVectors.includes(vector.id) ? "ring-2 ring-primary bg-primary/5" : ""
                  }`}
                  onClick={() => toggleVector(vector.id)}
                >
                  <CardContent className="p-3">
                    <div className="font-medium text-sm">{vector.name}</div>
                    <div className="text-xs text-muted-foreground">{vector.description}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Execution */}
      <Card>
        <CardHeader>
          <CardTitle>Test Execution</CardTitle>
          <CardDescription>Monitor and control your testing session</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Controls */}
          <div className="flex gap-4">
            <Button onClick={startTesting} disabled={isRunning}>
              <Play className="mr-2 h-4 w-4" />
              Start Testing
            </Button>
            <Button variant="outline" onClick={stopTesting} disabled={!isRunning}>
              <Square className="mr-2 h-4 w-4" />
              Stop
            </Button>
            <Button variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Schedule
            </Button>
          </div>

          {/* Status */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{testingStatus.status}</div>
              <div className="text-sm text-muted-foreground">Status</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{testingStatus.testsRun}</div>
              <div className="text-sm text-muted-foreground">Tests Run</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{testingStatus.successRate}</div>
              <div className="text-sm text-muted-foreground">Success Rate</div>
            </div>
          </div>

          {/* Results */}
          <div>
            <h4 className="font-medium mb-4">Live Results</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Attack Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Response Time</TableHead>
                  <TableHead>Result</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {results.map((result, index) => (
                  <TableRow key={index}>
                    <TableCell>{result.attackType}</TableCell>
                    <TableCell>
                      <Badge variant={result.status === "Success" ? "default" : "destructive"}>{result.status}</Badge>
                    </TableCell>
                    <TableCell>{result.responseTime}</TableCell>
                    <TableCell>{result.result}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
