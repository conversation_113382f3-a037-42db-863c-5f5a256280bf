"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from "recharts"

const monitoringMetrics = [
  { label: "Attacks Detected", value: "127", trend: "+15% from last week" },
  { label: "Block Rate", value: "98.2%", trend: "****% from last week" },
  { label: "Critical Threats", value: "3", trend: "-1 from yesterday" },
]

const threatData = [
  { name: "Prompt Injection", value: 45, color: "hsl(var(--primary))" },
  { name: "Jailbreak Attempts", value: 23, color: "hsl(var(--secondary))" },
  { name: "Data Extraction", value: 18, color: "hsl(var(--destructive))" },
  { name: "Model Poisoning", value: 8, color: "hsl(var(--muted))" },
  { name: "Adversarial Examples", value: 33, color: "hsl(var(--accent))" },
]

export function Protect() {
  return (
    <div className="space-y-6">
      {/* Monitoring Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle>Continuous Monitoring</CardTitle>
          <CardDescription>Real-time security metrics and threat detection</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {monitoringMetrics.map((metric, index) => (
              <div key={index} className="text-center space-y-2">
                <div className="text-3xl font-bold text-primary">{metric.value}</div>
                <div className="font-medium">{metric.label}</div>
                <div className="text-sm text-muted-foreground">{metric.trend}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Threat Detection Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Real-time Threat Detection</CardTitle>
          <CardDescription>Distribution of detected attack types</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={threatData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {threatData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Alert Management */}
      <Card>
        <CardHeader>
          <CardTitle>Alert Management</CardTitle>
          <CardDescription>Configure notifications and alert settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex gap-4">
            <Button>Configure Alerts</Button>
            <Button variant="outline">View History</Button>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="email-notifications">Email Notifications</Label>
              <Switch id="email-notifications" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="slack-integration">Slack Integration</Label>
              <Switch id="slack-integration" />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="alert-threshold">Alert Threshold</Label>
              <Select defaultValue="medium">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
