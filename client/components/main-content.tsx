"use client"

import { SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"

import { Dashboard } from "@/components/sections/dashboard"
import { GenerateDataset } from "@/components/sections/generate-dataset"
import { AutomateTesting } from "@/components/sections/automate-testing"
import { Evaluation } from "@/components/sections/evaluation"
import { ExpertReview } from "@/components/sections/expert-review"
import { Protect } from "@/components/sections/protect"
import { Settings } from "@/components/sections/settings"


interface MainContentProps {
  currentSection: string
}

const sectionTitles = {
  dashboard: "Dashboard",
  "generate-dataset": "Generate Dataset",
  "automate-testing": "Automate Testing",
  evaluation: "Evaluation",
  "expert-review": "Expert Review",
  protect: "Protect",
  settings: "Settings",
}

export function MainContent({ currentSection }: MainContentProps) {

  const renderSection = () => {
    switch (currentSection) {
      case "dashboard":
        return <Dashboard />
      case "generate-dataset":
        return <GenerateDataset />
      case "automate-testing":
        return <AutomateTesting />
      case "evaluation":
        return <Evaluation />
      case "expert-review":
        return <ExpertReview />
      case "protect":
        return <Protect />
      case "settings":
        return <Settings />
      default:
        return <Dashboard />
    }
  }

  return (
    <SidebarInset>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <h1 className="text-xl font-semibold">{sectionTitles[currentSection as keyof typeof sectionTitles]}</h1>
        </div>
      </header>
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{renderSection()}</div>
    </SidebarInset>
  )
}
