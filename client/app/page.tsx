"use client"

import { useState } from "react"
import { SidebarProvider } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { MainContent } from "@/components/main-content"

export default function Home() {
  const [currentSection, setCurrentSection] = useState("dashboard")

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex min-h-screen w-full">
        <AppSidebar currentSection={currentSection} onSectionChange={setCurrentSection} />
        <MainContent currentSection={currentSection} />
      </div>
    </SidebarProvider>
  )
}
