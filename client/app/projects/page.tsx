"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { useState, useEffect } from "react"
import { projectsApi } from "@/lib/api-client"
import Link from "next/link"

interface Project {
  id: string
  name: string
  description: string
  domain: string
  status: "active" | "completed" | "pending"
  datasetsGenerated: number
  testsRun: number
  riskScore: number
  progress: number
  created_at: string
  updated_at: string
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case "active":
      return "default"
    case "completed":
      return "secondary"
    case "pending":
      return "outline"
    default:
      return "outline"
  }
}

const getRiskColor = (score: number) => {
  if (score < 2) return "text-green-600"
  if (score < 3) return "text-yellow-600"
  return "text-red-600"
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await projectsApi.list()

        if (response.error) {
          throw new Error(response.error)
        }

        if (response.data) {
          setProjects(response.data.projects || [])
        } else {
          setProjects([])
        }
      } catch (err) {
        console.error('Error fetching projects:', err)
        setError(err instanceof Error ? err.message : 'Failed to load projects')
        setProjects([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchProjects()
  }, [])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">All Projects</h1>
        <Button asChild>
          <Link href="/">Back to Dashboard</Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2 text-muted-foreground">Loading projects...</span>
        </div>
      ) : error ? (
        <Card className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p className="font-medium">Failed to load projects</p>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        </Card>
      ) : projects.length === 0 ? (
        <Card className="p-6">
          <div className="text-center text-muted-foreground">
            <p className="font-medium">No projects found</p>
            <p className="text-sm mt-1">Create your first project to get started</p>
          </div>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project) => (
            <Card key={project.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-base">{project.name}</CardTitle>
                  <Badge variant={getStatusVariant(project.status)}>{project.status}</Badge>
                </div>
                <CardDescription>{project.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Datasets: {project.datasetsGenerated}</span>
                  <span>Tests: {project.testsRun}</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-2" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Risk Score:</span>
                  <span className={`font-medium ${getRiskColor(project.riskScore)}`}>
                    {project.riskScore}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}