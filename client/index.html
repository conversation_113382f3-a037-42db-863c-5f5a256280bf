<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deepassure - LLM Testing Platform</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-logo">Deepassure</h2>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <ul class="sidebar-nav">
                <li><a href="#dashboard" class="nav-link active" data-section="dashboard">Dashboard</a></li>
                <li><a href="#generate-dataset" class="nav-link" data-section="generate-dataset">Generate Dataset</a></li>
                <li><a href="#automate-testing" class="nav-link" data-section="automate-testing">Automate Testing</a></li>
                <li><a href="#evaluation" class="nav-link" data-section="evaluation">Evaluation</a></li>
                <li><a href="#protect" class="nav-link" data-section="protect">Protect</a></li>
                <li><a href="#settings" class="nav-link" data-section="settings">Settings</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-content">
                    <h1 class="page-title" id="pageTitle">Dashboard</h1>
                    <div class="header-actions">
                        <button class="btn btn--primary" id="newProjectBtn">New Project</button>
                        <div class="user-profile">
                            <span class="user-name">John Doe</span>
                            <div class="user-avatar">JD</div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Section -->
            <section class="section active" id="dashboard">
                <!-- Metrics Cards -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="totalProjects">15</div>
                        <div class="metric-label">Total Projects</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="activeTesting">8</div>
                        <div class="metric-label">Active Testing</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="averageRiskScore">2.1</div>
                        <div class="metric-label">Average Risk Score</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="testsThisWeek">2,340</div>
                        <div class="metric-label">Tests This Week</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="criticalAlerts">3</div>
                        <div class="metric-label">Critical Alerts</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="datasetsGenerated">12,500</div>
                        <div class="metric-label">Datasets Generated</div>
                    </div>
                </div>

                <!-- Recent Projects -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>Recent Projects</h3>
                        <button class="btn btn--secondary">View All</button>
                    </div>
                    <div class="projects-grid" id="recentProjects">
                        <!-- Projects will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Recent Alerts -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>Recent Alerts</h3>
                        <button class="btn btn--secondary">View All</button>
                    </div>
                    <div class="alerts-list" id="recentAlerts">
                        <!-- Alerts will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn--primary">Run Test</button>
                    <button class="btn btn--secondary">View Reports</button>
                    <button class="btn btn--outline">Schedule Testing</button>
                </div>
            </section>

            <!-- Generate Dataset Section -->
            <section class="section" id="generate-dataset">
                <div class="wizard-container">
                    <div class="wizard-header">
                        <div class="wizard-steps">
                            <div class="wizard-step active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-label">Application Description</div>
                            </div>
                            <div class="wizard-step" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-label">Test Configuration</div>
                            </div>
                            <div class="wizard-step" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-label">Expert Collaboration</div>
                            </div>
                            <div class="wizard-step" data-step="4">
                                <div class="step-number">4</div>
                                <div class="step-label">Generation Progress</div>
                            </div>
                        </div>
                    </div>

                    <div class="wizard-content">
                        <!-- Step 1: Application Description -->
                        <div class="wizard-panel active" data-panel="1">
                            <div class="form-group">
                                <label class="form-label">Application Description</label>
                                <textarea class="form-control" id="appDescription" rows="6" placeholder="Describe your LLM application, its purpose, and key functionality..."></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Application Domain</label>
                                <select class="form-control" id="appDomain">
                                    <option value="">Select domain...</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="finance">Finance</option>
                                    <option value="customer-service">Customer Service</option>
                                    <option value="education">Education</option>
                                    <option value="legal">Legal</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Example Input</label>
                                <textarea class="form-control" id="exampleInput" rows="3" placeholder="Provide example inputs your application receives..."></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Reference Documents</label>
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <div class="upload-icon">📁</div>
                                        <div class="upload-text">Drag and drop files here or click to upload</div>
                                        <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.txt">
                                    </div>
                                </div>
                                <div class="uploaded-files" id="uploadedFiles"></div>
                            </div>
                        </div>

                        <!-- Step 2: Test Configuration -->
                        <div class="wizard-panel" data-panel="2">
                            <div class="config-grid">
                                <div class="config-section">
                                    <h4>Test Types</h4>
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="qa" checked> Quality Assurance
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="safety"> Safety Testing
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="bias"> Bias Detection
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="performance"> Performance Testing
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="security"> Security Testing
                                        </label>
                                    </div>
                                </div>
                                <div class="config-section">
                                    <h4>Parameters</h4>
                                    <div class="slider-group">
                                        <label class="slider-label">Test Complexity</label>
                                        <input type="range" class="slider" id="complexity" min="1" max="10" value="5">
                                        <div class="slider-value">5</div>
                                    </div>
                                    <div class="slider-group">
                                        <label class="slider-label">Coverage</label>
                                        <input type="range" class="slider" id="coverage" min="10" max="100" value="70">
                                        <div class="slider-value">70%</div>
                                    </div>
                                    <div class="slider-group">
                                        <label class="slider-label">Dataset Size</label>
                                        <input type="range" class="slider" id="datasetSize" min="100" max="5000" value="1000">
                                        <div class="slider-value">1000</div>
                                    </div>
                                </div>
                            </div>
                            <div class="preview-section">
                                <h4>Preview Test Scenarios</h4>
                                <div class="scenario-preview" id="scenarioPreview">
                                    <div class="scenario-item">
                                        <div class="scenario-type">Safety Test</div>
                                        <div class="scenario-description">Test prompt injection resistance with malicious inputs</div>
                                    </div>
                                    <div class="scenario-item">
                                        <div class="scenario-type">Bias Test</div>
                                        <div class="scenario-description">Evaluate demographic bias in responses</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Expert Collaboration -->
                        <div class="wizard-panel" data-panel="3">
                            <div class="expert-finder">
                                <div class="expert-filters">
                                    <select class="form-control" id="expertSpecialty">
                                        <option value="">Filter by specialty...</option>
                                        <option value="healthcare">Healthcare AI</option>
                                        <option value="finance">Financial AI</option>
                                        <option value="security">AI Security</option>
                                        <option value="conversational">Conversational AI</option>
                                    </select>
                                    <select class="form-control" id="expertRating">
                                        <option value="">Filter by rating...</option>
                                        <option value="4.5">4.5+ stars</option>
                                        <option value="4.0">4.0+ stars</option>
                                        <option value="3.5">3.5+ stars</option>
                                    </select>
                                </div>
                                <div class="experts-grid" id="expertsGrid">
                                    <!-- Expert cards will be populated by JavaScript -->
                                </div>
                            </div>
                            <div class="collaboration-workspace">
                                <h4>Collaboration Workspace</h4>
                                <div class="chat-interface">
                                    <div class="chat-messages" id="chatMessages">
                                        <div class="message expert-message">
                                            <div class="message-author">Dr. Sarah Chen</div>
                                            <div class="message-content">I've reviewed your healthcare scenarios. We should add edge cases for patient privacy.</div>
                                        </div>
                                        <div class="message user-message">
                                            <div class="message-author">You</div>
                                            <div class="message-content">Great suggestion! Can you provide specific examples?</div>
                                        </div>
                                    </div>
                                    <div class="chat-input">
                                        <input type="text" class="form-control" id="chatInput" placeholder="Type your message...">
                                        <button class="btn btn--primary" id="sendMessage">Send</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Generation Progress -->
                        <div class="wizard-panel" data-panel="4">
                            <div class="progress-container">
                                <div class="progress-steps">
                                    <div class="progress-step completed">
                                        <div class="progress-icon">✓</div>
                                        <div class="progress-label">Processing</div>
                                    </div>
                                    <div class="progress-step active">
                                        <div class="progress-icon">⏳</div>
                                        <div class="progress-label">Generating</div>
                                    </div>
                                    <div class="progress-step">
                                        <div class="progress-icon">○</div>
                                        <div class="progress-label">Validating</div>
                                    </div>
                                    <div class="progress-step">
                                        <div class="progress-icon">○</div>
                                        <div class="progress-label">Complete</div>
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progressFill" style="width: 45%"></div>
                                </div>
                            </div>
                            <div class="logs-panel">
                                <h4>Generation Logs</h4>
                                <div class="logs-content" id="logsContent">
                                    <div class="log-entry">Processing application description...</div>
                                    <div class="log-entry">Generating test scenarios based on domain: Healthcare</div>
                                    <div class="log-entry">Created 156 safety test cases</div>
                                    <div class="log-entry">Validating scenario diversity...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="wizard-actions">
                        <button class="btn btn--secondary" id="prevStep">Previous</button>
                        <button class="btn btn--primary" id="nextStep">Next</button>
                    </div>
                </div>
            </section>

            <!-- Automate Testing Section -->
            <section class="section" id="automate-testing">
                <div class="testing-container">
                    <div class="testing-config">
                        <h3>Target Configuration</h3>
                        <div class="config-form">
                            <div class="form-group">
                                <label class="form-label">Endpoint URL</label>
                                <input type="url" class="form-control" id="endpointUrl" placeholder="https://api.example.com/llm">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Authentication</label>
                                <select class="form-control" id="authType">
                                    <option value="bearer">Bearer Token</option>
                                    <option value="api-key">API Key</option>
                                    <option value="oauth">OAuth 2.0</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">API Key/Token</label>
                                <input type="password" class="form-control" id="apiKey" placeholder="Enter your API key">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Rate Limit (requests/minute)</label>
                                <input type="number" class="form-control" id="rateLimit" value="60">
                            </div>
                        </div>
                    </div>

                    <div class="attack-vectors">
                        <h3>Attack Vectors</h3>
                        <div class="vectors-grid" id="attackVectors">
                            <!-- Attack vectors will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="testing-dashboard">
                        <h3>Test Execution</h3>
                        <div class="execution-controls">
                            <button class="btn btn--primary" id="startTesting">Start Testing</button>
                            <button class="btn btn--secondary" id="stopTesting">Stop</button>
                            <button class="btn btn--outline" id="scheduleTests">Schedule</button>
                        </div>
                        <div class="execution-status" id="executionStatus">
                            <div class="status-item">
                                <div class="status-label">Status</div>
                                <div class="status-value">Ready</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">Tests Run</div>
                                <div class="status-value">0</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">Success Rate</div>
                                <div class="status-value">-</div>
                            </div>
                        </div>
                        <div class="results-panel">
                            <h4>Live Results</h4>
                            <div class="results-table" id="resultsTable">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Attack Type</th>
                                            <th>Status</th>
                                            <th>Response Time</th>
                                            <th>Result</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsBody">
                                        <!-- Results will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Evaluation Section -->
            <section class="section" id="evaluation">
                <div class="evaluation-container">
                    <div class="metrics-comparison">
                        <h3>LLM Judge vs Domain Expert Alignment</h3>
                        <div class="chart-container">
                            <canvas id="alignmentChart"></canvas>
                        </div>
                    </div>
                    <div class="evaluation-metrics">
                        <div class="metrics-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Metric</th>
                                        <th>LLM Judge</th>
                                        <th>Domain Expert</th>
                                        <th>Alignment %</th>
                                    </tr>
                                </thead>
                                <tbody id="metricsTableBody">
                                    <!-- Metrics will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="evaluation-actions">
                        <button class="btn btn--primary">Generate Report</button>
                        <button class="btn btn--secondary">Export Data</button>
                        <button class="btn btn--outline">View Details</button>
                    </div>
                </div>
            </section>

            <!-- Protect Section -->
            <section class="section" id="protect">
                <div class="protect-container">
                    <div class="monitoring-dashboard">
                        <h3>Continuous Monitoring</h3>
                        <div class="monitoring-grid">
                            <div class="monitoring-card">
                                <div class="monitoring-metric">127</div>
                                <div class="monitoring-label">Attacks Detected</div>
                                <div class="monitoring-trend">+15% from last week</div>
                            </div>
                            <div class="monitoring-card">
                                <div class="monitoring-metric">98.2%</div>
                                <div class="monitoring-label">Block Rate</div>
                                <div class="monitoring-trend">+2.1% from last week</div>
                            </div>
                            <div class="monitoring-card">
                                <div class="monitoring-metric">3</div>
                                <div class="monitoring-label">Critical Threats</div>
                                <div class="monitoring-trend">-1 from yesterday</div>
                            </div>
                        </div>
                    </div>
                    <div class="threat-detection">
                        <h3>Real-time Threat Detection</h3>
                        <div class="threat-chart">
                            <canvas id="threatChart"></canvas>
                        </div>
                    </div>
                    <div class="alert-management">
                        <h3>Alert Management</h3>
                        <div class="alert-controls">
                            <button class="btn btn--primary">Configure Alerts</button>
                            <button class="btn btn--secondary">View History</button>
                        </div>
                        <div class="alert-settings">
                            <div class="setting-item">
                                <label class="form-label">Email Notifications</label>
                                <input type="checkbox" id="emailNotifications" checked>
                            </div>
                            <div class="setting-item">
                                <label class="form-label">Slack Integration</label>
                                <input type="checkbox" id="slackIntegration">
                            </div>
                            <div class="setting-item">
                                <label class="form-label">Alert Threshold</label>
                                <select class="form-control" id="alertThreshold">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section class="section" id="settings">
                <div class="settings-container">
                    <h3>Settings</h3>
                    <div class="settings-grid">
                        <div class="settings-section">
                            <h4>General</h4>
                            <div class="setting-item">
                                <label class="form-label">Theme</label>
                                <select class="form-control" id="themeSelector">
                                    <option value="auto">Auto</option>
                                    <option value="light">Light</option>
                                    <option value="dark" selected>Dark</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <label class="form-label">Language</label>
                                <select class="form-control">
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                </select>
                            </div>
                        </div>
                        <div class="settings-section">
                            <h4>Notifications</h4>
                            <div class="setting-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked> Email notifications
                                </label>
                            </div>
                            <div class="setting-item">
                                <label class="checkbox-label">
                                    <input type="checkbox"> Push notifications
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div class="modal" id="newProjectModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Project</h3>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Project Name</label>
                    <input type="text" class="form-control" id="projectName" placeholder="Enter project name">
                </div>
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <textarea class="form-control" id="projectDescription" rows="3" placeholder="Describe your project"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Domain</label>
                    <select class="form-control" id="projectDomain">
                        <option value="">Select domain...</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="finance">Finance</option>
                        <option value="customer-service">Customer Service</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn--secondary" id="cancelProject">Cancel</button>
                <button class="btn btn--primary" id="createProject">Create Project</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
