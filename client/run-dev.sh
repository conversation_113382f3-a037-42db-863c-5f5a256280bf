#!/bin/bash

# Load NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" || {
  echo "Error: NVM not found. Please install NVM first."
  exit 1
}

# Set environment variables
export NODE_ENV=development
export PORT=3001
export NEXT_PUBLIC_API_URL=http://localhost:8000

# Check for .nvmrc
if [ -f ".nvmrc" ]; then
  nvm use || {
    echo "Error: Failed to use Node version from .nvmrc"
    exit 1
  }
else
  nvm use 20 || {
    echo "Error: Failed to use Node.js 20"
    exit 1
  }
fi

# Check node version
REQUIRED_NODE="20"
CURRENT_NODE=$(node -v | cut -d'.' -f1 | tr -d 'v')

if [ "$CURRENT_NODE" -lt "$REQUIRED_NODE" ]; then
  echo "Error: Node.js $REQUIRED_NODE or higher required"
  exit 1
fi

# Check and install pnpm if needed
if ! command -v pnpm &> /dev/null; then
  echo "Installing pnpm..."
  npm install -g pnpm || {
    echo "Error: Failed to install pnpm"
    exit 1
  }
  # Get the exact path of the installed pnpm
  PNPM_PATH=$(npm bin -g)/pnpm
else
  PNPM_PATH=$(which pnpm)
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  "$PNPM_PATH" install || {
    echo "Error: Failed to install dependencies"
    exit 1
  }
fi

# Function to check port availability
check_port() {
  if lsof -i :${PORT} > /dev/null 2>&1; then
    echo "Port ${PORT} is already in use by:"
    lsof -i :${PORT}
    return 1
  fi
  return 0
}

# Check and resolve port conflicts
resolve_port_conflict() {
  while ! check_port; do
    echo -e "\nPort ${PORT} is already in use by:"
    lsof -i :${PORT}
    echo -e "\nOptions:"
    echo "1) Kill existing process on port ${PORT}"
    echo "2) Use a different port"
    echo "3) Exit"
    read -p "Choose option [1-3]: " choice
    
    case $choice in
      1)
        echo "Killing process on port ${PORT}..."
        kill -9 $(lsof -t -i :${PORT}) 2>/dev/null || {
          echo "Failed to kill process"
          exit 1
        }
        ;;
      2)
        read -p "Enter new port number: " PORT
        export PORT
        ;;
      3)
        exit 0
        ;;
      *)
        echo "Invalid option"
        exit 1
        ;;
    esac
  done
}

# Main execution
echo "Checking port availability..."
resolve_port_conflict

# Only start server if port is confirmed available
if check_port; then
  echo "Starting development server on port ${PORT}..."
  # Explicitly set PORT for Next.js
  NODE_ENV=development PORT=${PORT} "$PNPM_PATH" run dev
else
  echo "Failed to resolve port conflict. Exiting."
  exit 1
fi