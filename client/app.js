// Application data
const appData = {
  "testProjects": [
    {
      "id": "proj_001",
      "name": "Customer Support Chatbot",
      "description": "Testing conversational AI for customer service applications",
      "status": "active",
      "created": "2025-01-15",
      "datasetsGenerated": 1250,
      "testsRun": 890,
      "riskScore": 2.3,
      "lastActivity": "2025-01-20"
    },
    {
      "id": "proj_002", 
      "name": "Financial Advisory Bot",
      "description": "LLM testing for financial recommendation system",
      "status": "completed",
      "created": "2025-01-10",
      "datasetsGenerated": 890,
      "testsRun": 650,
      "riskScore": 1.8,
      "lastActivity": "2025-01-18"
    },
    {
      "id": "proj_003",
      "name": "Medical Diagnosis Assistant",
      "description": "Healthcare AI safety and accuracy testing",
      "status": "pending",
      "created": "2025-01-12",
      "datasetsGenerated": 320,
      "testsRun": 0,
      "riskScore": 0,
      "lastActivity": "2025-01-12"
    }
  ],
  "dashboardMetrics": {
    "totalProjects": 15,
    "activeTesting": 8,
    "averageRiskScore": 2.1,
    "testsThisWeek": 2340,
    "criticalAlerts": 3,
    "datasetsGenerated": 12500,
    "expertCollaborations": 45,
    "attacksDetected": 127
  },
  "recentAlerts": [
    {
      "id": "alert_001",
      "type": "critical",
      "title": "Prompt Injection Detected",
      "description": "High-risk prompt injection attempt bypassed safety filters",
      "project": "Customer Support Chatbot",
      "timestamp": "2025-01-20 14:30"
    },
    {
      "id": "alert_002", 
      "type": "warning",
      "title": "Model Drift Detected",
      "description": "Performance degradation observed in financial advisory responses",
      "project": "Financial Advisory Bot",
      "timestamp": "2025-01-20 11:45"
    },
    {
      "id": "alert_003",
      "type": "info",
      "title": "Dataset Generation Complete",
      "description": "Successfully generated 250 new test scenarios",
      "project": "Medical Diagnosis Assistant", 
      "timestamp": "2025-01-20 09:15"
    }
  ],
  "evaluationMetrics": [
    {"name": "Accuracy", "llmJudge": 87.5, "domainExpert": 89.2, "alignment": 94.1},
    {"name": "Safety", "llmJudge": 92.1, "domainExpert": 88.7, "alignment": 96.2},
    {"name": "Relevance", "llmJudge": 84.3, "domainExpert": 87.6, "alignment": 92.8},
    {"name": "Consistency", "llmJudge": 91.2, "domainExpert": 89.9, "alignment": 98.5},
    {"name": "Bias Detection", "llmJudge": 76.8, "domainExpert": 82.1, "alignment": 89.4}
  ],
  "attackTypes": [
    {"name": "Prompt Injection", "detected": 45, "blocked": 42, "severity": "high"},
    {"name": "Jailbreak Attempts", "detected": 23, "blocked": 21, "severity": "critical"},
    {"name": "Data Extraction", "detected": 18, "blocked": 16, "severity": "high"},
    {"name": "Model Poisoning", "detected": 8, "blocked": 8, "severity": "critical"},
    {"name": "Adversarial Examples", "detected": 33, "blocked": 29, "severity": "medium"}
  ],
  "domainExperts": [
    {"id": "expert_001", "name": "Dr. Sarah Chen", "specialty": "Healthcare AI", "rating": 4.9, "collaborations": 12},
    {"id": "expert_002", "name": "Prof. Michael Rodriguez", "specialty": "Financial AI Ethics", "rating": 4.8, "collaborations": 8},
    {"id": "expert_003", "name": "Dr. Emily Watson", "specialty": "Conversational AI", "rating": 4.7, "collaborations": 15},
    {"id": "expert_004", "name": "Alex Kim", "specialty": "AI Security", "rating": 4.9, "collaborations": 20}
  ]
};

// Global variables
let currentSection = 'dashboard';
let currentWizardStep = 1;
let uploadedFiles = [];
let selectedExperts = [];
let selectedVectors = [];
let charts = {};
let testingInterval = null;
let progressInterval = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeDashboard();
    initializeWizard();
    initializeFileUpload();
    initializeExpertSelection();
    initializeTestingInterface();
    initializeEvaluation();
    initializeProtection();
    initializeModals();
    initializeSettings();
});

// Navigation
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.section');
    const pageTitle = document.getElementById('pageTitle');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Show/hide sections
            sections.forEach(section => {
                section.classList.remove('active');
                if (section.id === sectionId) {
                    section.classList.add('active');
                }
            });
            
            // Update page title
            pageTitle.textContent = this.textContent;
            currentSection = sectionId;
            
            // Initialize section-specific functionality
            if (sectionId === 'evaluation') {
                setTimeout(() => initializeEvaluationCharts(), 100);
            } else if (sectionId === 'protect') {
                setTimeout(() => initializeProtectionCharts(), 100);
            }
        });
    });

    // Mobile sidebar toggle
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('active');
    });
}

// Dashboard
function initializeDashboard() {
    populateMetrics();
    populateRecentProjects();
    populateRecentAlerts();
}

function populateMetrics() {
    const metrics = appData.dashboardMetrics;
    document.getElementById('totalProjects').textContent = metrics.totalProjects;
    document.getElementById('activeTesting').textContent = metrics.activeTesting;
    document.getElementById('averageRiskScore').textContent = metrics.averageRiskScore;
    document.getElementById('testsThisWeek').textContent = metrics.testsThisWeek.toLocaleString();
    document.getElementById('criticalAlerts').textContent = metrics.criticalAlerts;
    document.getElementById('datasetsGenerated').textContent = metrics.datasetsGenerated.toLocaleString();
}

function populateRecentProjects() {
    const projectsContainer = document.getElementById('recentProjects');
    projectsContainer.innerHTML = '';
    
    appData.testProjects.forEach(project => {
        const progressPercentage = project.testsRun > 0 ? (project.testsRun / project.datasetsGenerated) * 100 : 0;
        const riskLevel = getRiskLevel(project.riskScore);
        
        const projectCard = document.createElement('div');
        projectCard.className = 'project-card';
        projectCard.innerHTML = `
            <div class="project-header">
                <h4 class="project-name">${project.name}</h4>
                <span class="status status--${project.status}">${project.status}</span>
            </div>
            <p class="project-description">${project.description}</p>
            <div class="project-stats">
                <span>Datasets: ${project.datasetsGenerated}</span>
                <span>Tests: ${project.testsRun}</span>
                <span>Created: ${project.created}</span>
            </div>
            <div class="project-progress">
                <div class="project-progress-fill" style="width: ${progressPercentage}%"></div>
            </div>
            <div class="project-risk">
                <span>Risk Score:</span>
                <span class="risk-score ${riskLevel}">${project.riskScore}</span>
            </div>
        `;
        projectsContainer.appendChild(projectCard);
    });
}

function populateRecentAlerts() {
    const alertsContainer = document.getElementById('recentAlerts');
    alertsContainer.innerHTML = '';
    
    appData.recentAlerts.forEach(alert => {
        const alertItem = document.createElement('div');
        alertItem.className = `alert-item ${alert.type}`;
        alertItem.innerHTML = `
            <div class="alert-icon ${alert.type}">
                ${getAlertIcon(alert.type)}
            </div>
            <div class="alert-content">
                <div class="alert-title">${alert.title}</div>
                <div class="alert-description">${alert.description}</div>
                <div class="alert-meta">${alert.project} • ${alert.timestamp}</div>
            </div>
        `;
        alertsContainer.appendChild(alertItem);
    });
}

function getRiskLevel(score) {
    if (score < 2) return 'low';
    if (score < 3) return 'medium';
    return 'high';
}

function getAlertIcon(type) {
    switch(type) {
        case 'critical': return '⚠️';
        case 'warning': return '⚡';
        case 'info': return 'ℹ️';
        default: return '•';
    }
}

// Wizard functionality
function initializeWizard() {
    const nextBtn = document.getElementById('nextStep');
    const prevBtn = document.getElementById('prevStep');
    
    nextBtn.addEventListener('click', function() {
        if (currentWizardStep < 4) {
            if (validateWizardStep(currentWizardStep)) {
                currentWizardStep++;
                updateWizardStep();
            }
        } else {
            // Complete wizard
            alert('Dataset generation completed successfully!');
            currentWizardStep = 1;
            updateWizardStep();
        }
    });
    
    prevBtn.addEventListener('click', function() {
        if (currentWizardStep > 1) {
            currentWizardStep--;
            updateWizardStep();
        }
    });
    
    // Initialize sliders
    const sliders = document.querySelectorAll('.slider');
    sliders.forEach(slider => {
        const valueDisplay = slider.nextElementSibling;
        slider.addEventListener('input', function() {
            let value = this.value;
            if (this.id === 'coverage') {
                value += '%';
            }
            valueDisplay.textContent = value;
        });
    });
}

function updateWizardStep() {
    // Update step indicators
    const steps = document.querySelectorAll('.wizard-step');
    const panels = document.querySelectorAll('.wizard-panel');
    
    steps.forEach((step, index) => {
        step.classList.remove('active', 'completed');
        if (index + 1 === currentWizardStep) {
            step.classList.add('active');
        } else if (index + 1 < currentWizardStep) {
            step.classList.add('completed');
        }
    });
    
    // Update panels
    panels.forEach((panel, index) => {
        panel.classList.remove('active');
        if (index + 1 === currentWizardStep) {
            panel.classList.add('active');
        }
    });
    
    // Update buttons
    const nextBtn = document.getElementById('nextStep');
    const prevBtn = document.getElementById('prevStep');
    
    prevBtn.style.display = currentWizardStep === 1 ? 'none' : 'inline-flex';
    nextBtn.textContent = currentWizardStep === 4 ? 'Complete' : 'Next';
    
    // Special handling for step 4
    if (currentWizardStep === 4) {
        startDatasetGeneration();
    } else {
        // Clear any existing progress interval
        if (progressInterval) {
            clearInterval(progressInterval);
            progressInterval = null;
        }
    }
}

function validateWizardStep(step) {
    switch(step) {
        case 1:
            const description = document.getElementById('appDescription').value;
            const domain = document.getElementById('appDomain').value;
            const exampleInput = document.getElementById('exampleInput').value;
            
            if (!description.trim()) {
                alert('Please provide an application description');
                return false;
            }
            if (!domain) {
                alert('Please select an application domain');
                return false;
            }
            if (!exampleInput.trim()) {
                alert('Please provide an example input');
                return false;
            }
            return true;
            
        case 2:
            const checkedTypes = document.querySelectorAll('input[type="checkbox"]:checked');
            if (checkedTypes.length === 0) {
                alert('Please select at least one test type');
                return false;
            }
            return true;
            
        case 3:
            if (selectedExperts.length === 0) {
                alert('Please select at least one domain expert');
                return false;
            }
            return true;
            
        default:
            return true;
    }
}

function startDatasetGeneration() {
    const progressFill = document.getElementById('progressFill');
    const logsContent = document.getElementById('logsContent');
    const progressSteps = document.querySelectorAll('.progress-step');
    
    // Clear existing logs
    logsContent.innerHTML = '';
    
    // Reset progress
    progressFill.style.width = '0%';
    progressSteps.forEach(step => {
        step.classList.remove('completed', 'active');
    });
    progressSteps[0].classList.add('active');
    
    // Add initial log
    const initialLog = document.createElement('div');
    initialLog.className = 'log-entry';
    initialLog.textContent = 'Starting dataset generation...';
    logsContent.appendChild(initialLog);
    
    // Simulate progress
    let progress = 0;
    let stepIndex = 0;
    const logs = [
        'Processing application description...',
        'Analyzing domain requirements...',
        'Generating test scenarios...',
        'Validating scenario coverage...',
        'Applying expert feedback...',
        'Optimizing test distribution...',
        'Finalizing dataset structure...',
        'Running quality checks...',
        'Dataset generation completed!'
    ];
    
    progressInterval = setInterval(() => {
        progress += Math.random() * 15 + 5; // Progress between 5-20% each step
        
        if (progress > 100) {
            progress = 100;
            clearInterval(progressInterval);
            
            // Mark all steps as completed
            progressSteps.forEach(step => {
                step.classList.add('completed');
                step.classList.remove('active');
            });
            
            // Add completion log
            const completionLog = document.createElement('div');
            completionLog.className = 'log-entry';
            completionLog.textContent = 'Dataset generation completed successfully!';
            logsContent.appendChild(completionLog);
            logsContent.scrollTop = logsContent.scrollHeight;
            
            return;
        }
        
        // Update progress bar
        progressFill.style.width = `${progress}%`;
        
        // Update progress steps
        if (progress > 25 && stepIndex < 1) {
            progressSteps[0].classList.add('completed');
            progressSteps[0].classList.remove('active');
            progressSteps[1].classList.add('active');
            stepIndex = 1;
        } else if (progress > 50 && stepIndex < 2) {
            progressSteps[1].classList.add('completed');
            progressSteps[1].classList.remove('active');
            progressSteps[2].classList.add('active');
            stepIndex = 2;
        } else if (progress > 75 && stepIndex < 3) {
            progressSteps[2].classList.add('completed');
            progressSteps[2].classList.remove('active');
            progressSteps[3].classList.add('active');
            stepIndex = 3;
        }
        
        // Add random log entries
        if (Math.random() < 0.4) {
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.textContent = logs[Math.floor(Math.random() * logs.length)];
            logsContent.appendChild(logEntry);
            logsContent.scrollTop = logsContent.scrollHeight;
        }
    }, 800);
}

// File upload
function initializeFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadedFilesContainer = document.getElementById('uploadedFiles');
    
    uploadArea.addEventListener('click', () => fileInput.click());
    
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        const files = Array.from(e.dataTransfer.files);
        handleFiles(files);
    });
    
    fileInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        handleFiles(files);
    });
    
    function handleFiles(files) {
        files.forEach(file => {
            uploadedFiles.push(file);
            addFileToList(file);
        });
    }
    
    function addFileToList(file) {
        const fileItem = document.createElement('div');
        fileItem.className = 'uploaded-file';
        fileItem.innerHTML = `
            <div class="file-info">
                <span class="file-icon">📄</span>
                <div>
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                </div>
            </div>
            <button class="remove-file" onclick="removeFile('${file.name}')">×</button>
        `;
        uploadedFilesContainer.appendChild(fileItem);
    }
    
    window.removeFile = function(fileName) {
        uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
        const fileItems = uploadedFilesContainer.querySelectorAll('.uploaded-file');
        fileItems.forEach(item => {
            if (item.querySelector('.file-name').textContent === fileName) {
                item.remove();
            }
        });
    };
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Expert selection
function initializeExpertSelection() {
    populateExperts();
    
    const expertSpecialtyFilter = document.getElementById('expertSpecialty');
    const expertRatingFilter = document.getElementById('expertRating');
    
    expertSpecialtyFilter.addEventListener('change', filterExperts);
    expertRatingFilter.addEventListener('change', filterExperts);
    
    // Chat functionality
    const chatInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendMessage');
    
    sendButton.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
}

function populateExperts() {
    const expertsGrid = document.getElementById('expertsGrid');
    expertsGrid.innerHTML = '';
    
    appData.domainExperts.forEach(expert => {
        const expertCard = document.createElement('div');
        expertCard.className = 'expert-card';
        expertCard.setAttribute('data-expert-id', expert.id);
        expertCard.innerHTML = `
            <div class="expert-header">
                <div class="expert-avatar">${expert.name.split(' ').map(n => n[0]).join('')}</div>
                <div>
                    <h5 class="expert-name">${expert.name}</h5>
                </div>
            </div>
            <div class="expert-specialty">${expert.specialty}</div>
            <div class="expert-rating">
                <span>⭐ ${expert.rating}</span>
                <span>(${expert.collaborations} collaborations)</span>
            </div>
        `;
        
        expertCard.addEventListener('click', function() {
            toggleExpertSelection(expert.id, this);
        });
        
        expertsGrid.appendChild(expertCard);
    });
}

function toggleExpertSelection(expertId, cardElement) {
    const isSelected = selectedExperts.includes(expertId);
    
    if (isSelected) {
        selectedExperts = selectedExperts.filter(id => id !== expertId);
        cardElement.classList.remove('selected');
    } else {
        selectedExperts.push(expertId);
        cardElement.classList.add('selected');
    }
}

function filterExperts() {
    const specialty = document.getElementById('expertSpecialty').value;
    const rating = document.getElementById('expertRating').value;
    const expertCards = document.querySelectorAll('.expert-card');
    
    expertCards.forEach(card => {
        const expertId = card.getAttribute('data-expert-id');
        const expert = appData.domainExperts.find(e => e.id === expertId);
        
        let showCard = true;
        
        if (specialty && !expert.specialty.toLowerCase().includes(specialty.toLowerCase())) {
            showCard = false;
        }
        
        if (rating && expert.rating < parseFloat(rating)) {
            showCard = false;
        }
        
        card.style.display = showCard ? 'block' : 'none';
    });
}

function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const chatMessages = document.getElementById('chatMessages');
    const message = chatInput.value.trim();
    
    if (message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message user-message';
        messageElement.innerHTML = `
            <div class="message-author">You</div>
            <div class="message-content">${message}</div>
        `;
        chatMessages.appendChild(messageElement);
        chatInput.value = '';
        
        // Auto-scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Simulate expert response
        setTimeout(() => {
            const responses = [
                "I'll review the scenarios and provide feedback shortly.",
                "These test cases look comprehensive. Consider adding edge cases for privacy compliance.",
                "The bias detection scenarios need refinement for healthcare applications.",
                "I suggest increasing the complexity for safety-critical scenarios.",
                "The coverage looks good. Let's focus on adversarial examples next."
            ];
            
            const responseElement = document.createElement('div');
            responseElement.className = 'message expert-message';
            responseElement.innerHTML = `
                <div class="message-author">Expert</div>
                <div class="message-content">${responses[Math.floor(Math.random() * responses.length)]}</div>
            `;
            chatMessages.appendChild(responseElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 1000);
    }
}

// Testing interface
function initializeTestingInterface() {
    populateAttackVectors();
    
    const startBtn = document.getElementById('startTesting');
    const stopBtn = document.getElementById('stopTesting');
    const scheduleBtn = document.getElementById('scheduleTests');
    
    startBtn.addEventListener('click', startTesting);
    stopBtn.addEventListener('click', stopTesting);
    scheduleBtn.addEventListener('click', () => alert('Schedule testing feature coming soon!'));
}

function populateAttackVectors() {
    const vectorsGrid = document.getElementById('attackVectors');
    vectorsGrid.innerHTML = '';
    
    const vectors = [
        { name: 'Prompt Injection', description: 'Test resistance to malicious prompts' },
        { name: 'Jailbreak Attempts', description: 'Try to bypass safety constraints' },
        { name: 'Data Extraction', description: 'Attempt to extract training data' },
        { name: 'Model Poisoning', description: 'Test input manipulation attacks' },
        { name: 'Adversarial Examples', description: 'Use adversarial inputs to fool model' },
        { name: 'Bias Amplification', description: 'Test for biased outputs' }
    ];
    
    vectors.forEach((vector, index) => {
        const vectorCard = document.createElement('div');
        vectorCard.className = 'vector-card';
        vectorCard.setAttribute('data-vector-id', index);
        vectorCard.innerHTML = `
            <div class="vector-name">${vector.name}</div>
            <div class="vector-description">${vector.description}</div>
        `;
        
        vectorCard.addEventListener('click', function() {
            toggleVectorSelection(index, this);
        });
        
        vectorsGrid.appendChild(vectorCard);
    });
}

function toggleVectorSelection(vectorId, cardElement) {
    const isSelected = selectedVectors.includes(vectorId);
    
    if (isSelected) {
        selectedVectors = selectedVectors.filter(id => id !== vectorId);
        cardElement.classList.remove('selected');
    } else {
        selectedVectors.push(vectorId);
        cardElement.classList.add('selected');
    }
}

function startTesting() {
    const endpointUrl = document.getElementById('endpointUrl').value;
    const apiKey = document.getElementById('apiKey').value;
    
    if (!endpointUrl.trim()) {
        alert('Please provide an endpoint URL');
        return;
    }
    
    if (!apiKey.trim()) {
        alert('Please provide an API key');
        return;
    }
    
    if (selectedVectors.length === 0) {
        alert('Please select at least one attack vector');
        return;
    }
    
    // Update status
    const statusItems = document.querySelectorAll('.status-item');
    statusItems[0].querySelector('.status-value').textContent = 'Running';
    
    // Clear previous results
    const resultsBody = document.getElementById('resultsBody');
    resultsBody.innerHTML = '';
    
    // Start simulated testing
    let testsRun = 0;
    testingInterval = setInterval(() => {
        testsRun++;
        updateTestingStatus(testsRun);
        addTestResult();
        
        if (testsRun >= 20) {
            clearInterval(testingInterval);
            statusItems[0].querySelector('.status-value').textContent = 'Completed';
        }
    }, 1000);
}

function stopTesting() {
    if (testingInterval) {
        clearInterval(testingInterval);
        testingInterval = null;
    }
    
    const statusItems = document.querySelectorAll('.status-item');
    statusItems[0].querySelector('.status-value').textContent = 'Stopped';
}

function updateTestingStatus(testsRun) {
    const statusItems = document.querySelectorAll('.status-item');
    statusItems[1].querySelector('.status-value').textContent = testsRun;
    
    const successRate = Math.floor(Math.random() * 20) + 80;
    statusItems[2].querySelector('.status-value').textContent = `${successRate}%`;
}

function addTestResult() {
    const resultsBody = document.getElementById('resultsBody');
    const attackTypes = ['Prompt Injection', 'Jailbreak', 'Data Extraction', 'Model Poisoning'];
    const statuses = ['Success', 'Failed', 'Blocked'];
    
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${attackTypes[Math.floor(Math.random() * attackTypes.length)]}</td>
        <td><span class="status status--${Math.random() < 0.8 ? 'success' : 'error'}">${statuses[Math.floor(Math.random() * statuses.length)]}</span></td>
        <td>${Math.floor(Math.random() * 500) + 100}ms</td>
        <td>${Math.random() < 0.7 ? 'Blocked' : 'Detected'}</td>
    `;
    
    resultsBody.appendChild(row);
    
    // Keep only last 10 results
    while (resultsBody.children.length > 10) {
        resultsBody.removeChild(resultsBody.firstChild);
    }
}

// Evaluation
function initializeEvaluation() {
    populateEvaluationMetrics();
}

function populateEvaluationMetrics() {
    const metricsTable = document.getElementById('metricsTableBody');
    metricsTable.innerHTML = '';
    
    appData.evaluationMetrics.forEach(metric => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${metric.name}</td>
            <td>${metric.llmJudge}%</td>
            <td>${metric.domainExpert}%</td>
            <td><span class="text-${metric.alignment > 95 ? 'success' : metric.alignment > 90 ? 'warning' : 'error'}">${metric.alignment}%</span></td>
        `;
        metricsTable.appendChild(row);
    });
}

function initializeEvaluationCharts() {
    const ctx = document.getElementById('alignmentChart').getContext('2d');
    
    if (charts.alignmentChart) {
        charts.alignmentChart.destroy();
    }
    
    charts.alignmentChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: appData.evaluationMetrics.map(m => m.name),
            datasets: [{
                label: 'LLM Judge',
                data: appData.evaluationMetrics.map(m => m.llmJudge),
                backgroundColor: '#1FB8CD',
                borderColor: '#1FB8CD',
                borderWidth: 1
            }, {
                label: 'Domain Expert',
                data: appData.evaluationMetrics.map(m => m.domainExpert),
                backgroundColor: '#FFC185',
                borderColor: '#FFC185',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// Protection
function initializeProtection() {
    // This will be called when the protect section is activated
}

function initializeProtectionCharts() {
    const ctx = document.getElementById('threatChart').getContext('2d');
    
    if (charts.threatChart) {
        charts.threatChart.destroy();
    }
    
    charts.threatChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: appData.attackTypes.map(a => a.name),
            datasets: [{
                data: appData.attackTypes.map(a => a.detected),
                backgroundColor: ['#1FB8CD', '#FFC185', '#B4413C', '#ECEBD5', '#5D878F'],
                borderColor: ['#1FB8CD', '#FFC185', '#B4413C', '#ECEBD5', '#5D878F'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Modals
function initializeModals() {
    const newProjectBtn = document.getElementById('newProjectBtn');
    const modal = document.getElementById('newProjectModal');
    const closeModal = document.getElementById('closeModal');
    const cancelBtn = document.getElementById('cancelProject');
    const createBtn = document.getElementById('createProject');
    
    newProjectBtn.addEventListener('click', () => {
        modal.classList.add('active');
    });
    
    closeModal.addEventListener('click', () => {
        modal.classList.remove('active');
    });
    
    cancelBtn.addEventListener('click', () => {
        modal.classList.remove('active');
    });
    
    createBtn.addEventListener('click', () => {
        const name = document.getElementById('projectName').value;
        const description = document.getElementById('projectDescription').value;
        const domain = document.getElementById('projectDomain').value;
        
        if (!name || !description || !domain) {
            alert('Please fill in all fields');
            return;
        }
        
        // Simulate project creation
        alert('Project created successfully!');
        modal.classList.remove('active');
        
        // Clear form
        document.getElementById('projectName').value = '';
        document.getElementById('projectDescription').value = '';
        document.getElementById('projectDomain').value = '';
    });
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('active');
        }
    });
}

// Settings
function initializeSettings() {
    const themeSelector = document.getElementById('themeSelector');
    const emailNotifications = document.getElementById('emailNotifications');
    const slackIntegration = document.getElementById('slackIntegration');
    const alertThreshold = document.getElementById('alertThreshold');
    
    // Apply default theme
    applyTheme('dark');
    
    themeSelector.addEventListener('change', function() {
        const theme = this.value;
        applyTheme(theme);
    });
    
    // Save other settings
    [emailNotifications, slackIntegration, alertThreshold].forEach(element => {
        element.addEventListener('change', function() {
            // In a real app, this would save to backend
            console.log('Setting saved:', this.id, this.value || this.checked);
        });
    });
}

function applyTheme(theme) {
    if (theme === 'dark') {
        document.documentElement.setAttribute('data-color-scheme', 'dark');
    } else if (theme === 'light') {
        document.documentElement.setAttribute('data-color-scheme', 'light');
    } else {
        document.documentElement.removeAttribute('data-color-scheme');
    }
}

// Utility functions
function showLoading(element) {
    element.classList.add('loading');
}

function hideLoading(element) {
    element.classList.remove('loading');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification--${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('Application error:', e.error);
    showNotification('An error occurred. Please try again.', 'error');
});

// Cleanup intervals on page unload
window.addEventListener('beforeunload', function() {
    if (testingInterval) {
        clearInterval(testingInterval);
    }
    if (progressInterval) {
        clearInterval(progressInterval);
    }
});

// Initialize theme on load
document.addEventListener('DOMContentLoaded', function() {
    applyTheme('dark');
});
