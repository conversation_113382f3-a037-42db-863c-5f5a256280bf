# RainbowPlus Frontend Configuration Guide

This document explains how to configure the RainbowPlus frontend to connect to different backend servers.

## Quick Start

### Option 1: Using the Configuration Script (Recommended)

```bash
# Configure for local development
node scripts/configure-backend.js local

# Configure for a specific IP address
node scripts/configure-backend.js http://*************:8000

# Configure for staging
node scripts/configure-backend.js staging

# Configure for production
node scripts/configure-backend.js production

# Check current configuration
node scripts/configure-backend.js --current
```

### Option 2: Manual Configuration

1. Copy the example environment file:
   ```bash
   cp .env.example .env.local
   ```

2. Edit `.env.local` and update the backend URL:
   ```bash
   NEXT_PUBLIC_API_URL=http://your-backend-ip:8000
   ```

3. Restart the development server:
   ```bash
   ./run-dev.sh
   ```

## Configuration Options

### Environment Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `NEXT_PUBLIC_API_URL` | Backend API base URL | `http://localhost:8000` | `http://*************:8000` |
| `NEXT_PUBLIC_API_TIMEOUT` | API request timeout (ms) | `30000` | `60000` |
| `NEXT_PUBLIC_APP_VERSION` | Application version | `1.0.0` | `1.2.3` |
| `PORT` | Frontend server port | `3001` | `3000` |
| `NODE_ENV` | Environment mode | `development` | `production` |

### Preset Configurations

The configuration script includes several presets:

- **`local`**: `http://localhost:8000` - Backend on same machine
- **`local-ip`**: `http://*************:8000` - Backend on local network
- **`staging`**: `https://staging-api.rainbowplus.com` - Staging environment
- **`production`**: `https://api.rainbowplus.com` - Production environment

## Common Scenarios

### 1. Backend on Same Machine (Default)
```bash
node scripts/configure-backend.js local
```
or manually set:
```
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### 2. Backend on Different Machine (Local Network)
```bash
node scripts/configure-backend.js http://*************:8000
```
or manually set:
```
NEXT_PUBLIC_API_URL=http://*************:8000
```

### 3. Backend on Remote Server
```bash
node scripts/configure-backend.js http://your-server.com:8000
```
or manually set:
```
NEXT_PUBLIC_API_URL=http://your-server.com:8000
```

### 4. Production Deployment
```bash
node scripts/configure-backend.js https://api.rainbowplus.com
```
or manually set:
```
NEXT_PUBLIC_API_URL=https://api.rainbowplus.com
NODE_ENV=production
```

## How It Works

### 1. Configuration Priority

The frontend uses the following priority order for determining the backend URL:

1. **Runtime configuration** (for production deployments)
2. **Environment variables** (`NEXT_PUBLIC_API_URL`)
3. **Environment-specific defaults** based on `NODE_ENV`

### 2. API Proxy (Development Only)

In development mode, the Next.js server automatically proxies `/api/*` requests to the configured backend URL. This avoids CORS issues during development.

### 3. Direct API Calls (Production)

In production, the frontend makes direct API calls to the configured backend URL.

## Files Overview

- **`.env.local`** - Local environment configuration (not committed to git)
- **`.env.example`** - Example configuration file with all available options
- **`lib/config.ts`** - Centralized configuration management
- **`lib/api-client.ts`** - API client that uses the configuration
- **`next.config.mjs`** - Next.js configuration with API proxy setup
- **`scripts/configure-backend.js`** - Configuration helper script

## Troubleshooting

### Backend Connection Issues

1. **Check current configuration:**
   ```bash
   node scripts/configure-backend.js --current
   ```

2. **Verify backend is running:**
   ```bash
   curl http://your-backend-ip:8000/health
   ```

3. **Check network connectivity:**
   ```bash
   ping your-backend-ip
   ```

### CORS Issues

If you encounter CORS issues:

1. **In development**: Make sure the Next.js proxy is working (check `next.config.mjs`)
2. **In production**: Configure CORS on your backend server

### Environment Variables Not Loading

1. **Restart the development server** after changing `.env.local`
2. **Check file name** - it should be `.env.local`, not `.env`
3. **Verify variable names** - they must start with `NEXT_PUBLIC_` to be available in the browser

## Advanced Configuration

### Custom API Client

You can extend the API client in `lib/api-client.ts` to add custom headers, authentication, or other features:

```typescript
// Add authentication header
apiClient.defaultHeaders['Authorization'] = 'Bearer your-token';

// Add custom timeout for specific requests
const response = await apiClient.request('projects', { timeout: 60000 });
```

### Runtime Configuration

For production deployments where you need to change the backend URL without rebuilding:

```html
<!-- Add to your HTML head -->
<script>
  window.__RUNTIME_CONFIG__ = {
    API_URL: 'https://your-production-api.com'
  };
</script>
```

## Support

If you encounter issues with configuration:

1. Check the browser console for error messages
2. Verify the backend server is accessible
3. Ensure all environment variables are properly set
4. Restart both frontend and backend servers after configuration changes
