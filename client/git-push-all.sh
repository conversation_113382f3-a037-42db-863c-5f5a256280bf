#!/bin/bash

# Initialize git repository if not exists
if [ ! -d .git ]; then
  git init
  # Rename default branch to main if it exists as master
  if git branch | grep -q master; then
    git branch -m master main
  fi
fi

# Set remote URL if not configured
if ! git remote get-url origin &>/dev/null; then
  git remote <NAME_EMAIL>:delivery/deepassure/dacui.git
fi

# Verify remote URL is correct
current_remote=$(git remote get-url origin)
desired_remote="***************************:delivery/deepassure/dacui.git"

if [ "$current_remote" != "$desired_remote" ]; then
  git remote set-url origin "$desired_remote"
fi

# Determine branch name (main or master)
branch_name="main"
if ! git show-ref --verify --quiet refs/heads/main; then
  branch_name="master"
fi

# Add all files to git staging
git add .

# Commit changes with timestamp
commit_message="Update: $(date +'%Y-%m-%d %H:%M:%S')"
git commit -m "$commit_message" || { echo "Commit failed"; exit 1; }

# Try normal push first
if git push -u origin $branch_name; then
  echo "Successfully pushed $branch_name branch to remote repository"
  exit 0
fi

# If normal push failed, try pull first
echo "Push failed - attempting to pull remote changes first..."
if git pull origin $branch_name --rebase; then
  if git push -u origin $branch_name; then
    echo "Successfully pushed $branch_name branch to remote repository after pull"
    exit 0
  fi
fi

# If still failing, offer force push option
read -p "Push still failing. Force push? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  if git push -u origin $branch_name --force; then
    echo "Successfully force pushed $branch_name branch to remote repository"
    exit 0
  else
    echo "Force push failed"
    exit 1
  fi
fi

echo "Push aborted"
exit 1