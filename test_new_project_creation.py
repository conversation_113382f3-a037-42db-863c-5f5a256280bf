#!/usr/bin/env python3
"""
Test the new project creation functionality.
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_create_project_via_api():
    """Test creating a project via API to simulate the new project dialog."""
    print("🧪 Testing new project creation...")
    
    project_data = {
        "name": "My Custom Project",
        "description": "A project created through the new project dialog",
        "domain": "healthcare"
    }
    
    response = requests.post(f"{BASE_URL}/projects", json=project_data)
    
    if response.status_code == 200:
        project = response.json()
        print(f"✅ Project created successfully:")
        print(f"   ID: {project['id']}")
        print(f"   Name: {project['name']}")
        print(f"   Domain: {project['domain']}")
        
        # Test dataset generation with this project
        dataset_data = {
            "description": "Test dataset for custom project",
            "examples": ["Test example for custom project"],
            "task": "Content",
            "project_id": project['id'],
            "nickname": "Custom Project Test"
        }
        
        response = requests.post(f"{BASE_URL}/datasets/generate", json=dataset_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Dataset generation started with custom project: {result['job_id']}")
            return True
        else:
            print(f"❌ Dataset generation failed: {response.status_code}")
            return False
    else:
        print(f"❌ Project creation failed: {response.status_code} - {response.text}")
        return False

def main():
    print("🚀 Testing New Project Creation Functionality")
    print("=" * 50)
    
    success = test_create_project_via_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 New project creation test passed!")
        print("\n📋 How to use the new functionality:")
        print("1. Go to http://localhost:3002")
        print("2. Navigate to 'Generate Dataset'")
        print("3. In Step 1, click the 'New Project' button")
        print("4. Fill in project details and click 'Create Project'")
        print("5. The new project will be automatically selected")
        print("6. Continue with dataset generation")
    else:
        print("❌ New project creation test failed!")

if __name__ == "__main__":
    main()
