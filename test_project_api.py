#!/usr/bin/env python3
"""
Test script for the Project API functionality.
Tests the complete CRUD operations for projects.
"""

import requests
import json
import sys
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def test_create_project() -> Dict[str, Any]:
    """Test creating a new project."""
    print("🧪 Testing project creation...")
    
    project_data = {
        "name": "Test Project API",
        "description": "A test project created via API testing",
        "domain": "finance"
    }
    
    response = requests.post(f"{BASE_URL}/projects", json=project_data)
    
    if response.status_code == 200:
        project = response.json()
        print(f"✅ Project created successfully: {project['name']} (ID: {project['id']})")
        return project
    else:
        print(f"❌ Failed to create project: {response.status_code} - {response.text}")
        return None

def test_list_projects():
    """Test listing all projects."""
    print("\n🧪 Testing project listing...")
    
    response = requests.get(f"{BASE_URL}/projects")
    
    if response.status_code == 200:
        projects_data = response.json()
        projects = projects_data.get("projects", [])
        print(f"✅ Found {len(projects)} projects:")
        for project in projects:
            print(f"   - {project['name']} ({project['domain']}) - {project['id']}")
        return projects
    else:
        print(f"❌ Failed to list projects: {response.status_code} - {response.text}")
        return []

def test_get_project(project_id: str):
    """Test getting a specific project."""
    print(f"\n🧪 Testing get project {project_id}...")
    
    response = requests.get(f"{BASE_URL}/projects/{project_id}")
    
    if response.status_code == 200:
        project = response.json()
        print(f"✅ Retrieved project: {project['name']}")
        return project
    elif response.status_code == 404:
        print(f"❌ Project not found: {project_id}")
        return None
    else:
        print(f"❌ Failed to get project: {response.status_code} - {response.text}")
        return None

def test_update_project(project_id: str):
    """Test updating a project."""
    print(f"\n🧪 Testing update project {project_id}...")
    
    updated_data = {
        "name": "Updated Test Project API",
        "description": "An updated test project description",
        "domain": "healthcare"
    }
    
    response = requests.put(f"{BASE_URL}/projects/{project_id}", json=updated_data)
    
    if response.status_code == 200:
        project = response.json()
        print(f"✅ Project updated successfully: {project['name']}")
        return project
    elif response.status_code == 404:
        print(f"❌ Project not found for update: {project_id}")
        return None
    else:
        print(f"❌ Failed to update project: {response.status_code} - {response.text}")
        return None

def test_delete_project(project_id: str):
    """Test deleting a project."""
    print(f"\n🧪 Testing delete project {project_id}...")
    
    response = requests.delete(f"{BASE_URL}/projects/{project_id}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Project deleted successfully: {result.get('message', 'Deleted')}")
        return True
    elif response.status_code == 404:
        print(f"❌ Project not found for deletion: {project_id}")
        return False
    else:
        print(f"❌ Failed to delete project: {response.status_code} - {response.text}")
        return False

def test_duplicate_name():
    """Test creating a project with duplicate name."""
    print("\n🧪 Testing duplicate name validation...")
    
    project_data = {
        "name": "Duplicate Name Test",
        "description": "First project with this name",
        "domain": "education"
    }
    
    # Create first project
    response1 = requests.post(f"{BASE_URL}/projects", json=project_data)
    if response1.status_code != 200:
        print(f"❌ Failed to create first project: {response1.text}")
        return
    
    project1 = response1.json()
    print(f"✅ Created first project: {project1['id']}")
    
    # Try to create second project with same name
    response2 = requests.post(f"{BASE_URL}/projects", json=project_data)
    if response2.status_code == 400:
        print("✅ Duplicate name validation working correctly")
    else:
        print(f"❌ Duplicate name validation failed: {response2.status_code}")
    
    # Clean up
    requests.delete(f"{BASE_URL}/projects/{project1['id']}")

def main():
    """Run all tests."""
    print("🚀 Starting Project API Tests")
    print("=" * 50)
    
    try:
        # Test basic CRUD operations
        project = test_create_project()
        if not project:
            print("❌ Cannot continue tests without a project")
            return
        
        project_id = project['id']
        
        test_list_projects()
        test_get_project(project_id)
        test_update_project(project_id)
        test_list_projects()  # Check updated project
        
        # Test validation
        test_duplicate_name()
        
        # Clean up
        test_delete_project(project_id)
        test_list_projects()  # Verify deletion
        
        print("\n🎉 All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the API server. Make sure it's running on http://localhost:8000")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
