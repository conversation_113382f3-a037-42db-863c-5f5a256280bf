#!/usr/bin/env python3
"""
Trigger dataset generation for the test events to create actual samples.
"""

import requests
import json
import time

# API endpoints
BASE_URL = "http://localhost:8000"

def trigger_generation_for_event(event_id, project_id, description, example_input):
    """Trigger dataset generation for a specific event."""
    
    print(f"🚀 Triggering dataset generation for event: {event_id}")
    
    # Dataset generation request
    generation_data = {
        "description": description,
        "examples": [example_input] if example_input else [],
        "documents": [],  # Will use default documents
        "task": "Content",
        "project_id": project_id,
        "nickname": f"Expert Review Test - {description[:30]}..."
    }
    
    try:
        response = requests.post(f"{BASE_URL}/datasets/generate", json=generation_data)
        response.raise_for_status()
        result = response.json()
        
        print(f"✅ Dataset generation started:")
        print(f"   Job ID: {result.get('job_id')}")
        print(f"   Status: {result.get('status')}")
        print(f"   Message: {result.get('message')}")
        
        return result.get('job_id')
        
    except Exception as e:
        print(f"❌ Failed to start dataset generation: {e}")
        return None

def check_generation_status(job_id):
    """Check the status of dataset generation."""
    
    print(f"🔍 Checking generation status for job: {job_id}")
    
    # Note: We might need to implement a job status endpoint
    # For now, let's check if samples were created
    try:
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=5")
        response.raise_for_status()
        data = response.json()
        
        print(f"📊 Current samples in database: {data['total_count']}")
        
        if data['samples']:
            print("📝 Recent samples:")
            for sample in data['samples'][:3]:
                print(f"   • {sample.get('label', 'No label')}: {sample['content'][:60]}...")
        
        return data['total_count']
        
    except Exception as e:
        print(f"❌ Error checking samples: {e}")
        return 0

def get_test_events():
    """Get the test events we created."""
    
    print("🔍 Finding test events to generate datasets for...")
    
    try:
        # Get recent events with project linkage
        response = requests.get(f"{BASE_URL}/datagen/events?limit=10")
        response.raise_for_status()
        data = response.json()
        
        test_events = []
        for event in data['events']:
            # Look for events with project_id (real events, not demo)
            if event.get('project_id') and event.get('project_name'):
                test_events.append({
                    'id': event['id'],
                    'project_id': event['project_id'],
                    'project_name': event['project_name'],
                    'description': event.get('application_description', ''),
                    'example_input': event.get('example_input', ''),
                    'domain': event.get('domain', ''),
                    'experts': event.get('selected_experts', [])
                })
        
        print(f"📋 Found {len(test_events)} real events with project linkage:")
        for event in test_events:
            print(f"   • {event['project_name']} ({event['domain']}) - Experts: {', '.join(event['experts'])}")
        
        return test_events
        
    except Exception as e:
        print(f"❌ Error getting test events: {e}")
        return []

def main():
    """Main function to trigger dataset generation for test events."""
    
    print("🧪 Triggering Dataset Generation for Expert Review Testing")
    print("=" * 70)
    
    # Get test events
    test_events = get_test_events()
    
    if not test_events:
        print("❌ No test events found. Create some events first.")
        return
    
    # Check current sample count
    initial_samples = check_generation_status("initial")
    
    # Trigger generation for each test event
    job_ids = []
    for event in test_events[:2]:  # Limit to first 2 events to avoid overwhelming the system
        print(f"\n" + "="*50)
        job_id = trigger_generation_for_event(
            event['id'],
            event['project_id'],
            event['description'],
            event['example_input']
        )
        if job_id:
            job_ids.append(job_id)
    
    if job_ids:
        print(f"\n⏳ Waiting for dataset generation to complete...")
        print(f"   Started {len(job_ids)} generation jobs")
        
        # Wait a bit for generation to complete
        for i in range(6):  # Wait up to 30 seconds
            time.sleep(5)
            current_samples = check_generation_status(f"check-{i+1}")
            
            if current_samples > initial_samples:
                print(f"🎉 New samples detected! ({current_samples - initial_samples} new samples)")
                break
            else:
                print(f"   Still waiting... ({(i+1)*5}s elapsed)")
        
        # Final check
        print(f"\n📊 Final Results:")
        final_samples = check_generation_status("final")
        new_samples = final_samples - initial_samples
        
        if new_samples > 0:
            print(f"✅ Successfully generated {new_samples} new samples!")
            print(f"🌐 You can now test the expert review interface:")
            print(f"   1. Open http://localhost:3002")
            print(f"   2. Navigate to 'Expert Review'")
            print(f"   3. Click on events to see their samples")
            print(f"   4. Use the expert dropdown to switch views")
        else:
            print(f"⚠️ No new samples were generated.")
            print(f"   This might be due to API limits or configuration issues.")
            print(f"   Check the server logs for more details.")
    
    else:
        print(f"\n❌ Failed to start any dataset generation jobs.")

if __name__ == "__main__":
    main()
