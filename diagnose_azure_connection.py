#!/usr/bin/env python3
"""
Azure PostgreSQL connection diagnostic tool.
Helps troubleshoot connection issues with Azure PostgreSQL.

Usage:
    python diagnose_azure_connection.py
"""

import os
import sys
import socket
import ssl
from pathlib import Path
from urllib.parse import quote_plus

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_basic_connectivity(host: str, port: int = 5432):
    """Test basic TCP connectivity to the PostgreSQL server."""
    print(f"🔗 Testing TCP connectivity to {host}:{port}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ TCP connection successful to {host}:{port}")
            return True
        else:
            print(f"❌ TCP connection failed to {host}:{port} (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ TCP connection error: {e}")
        return False


def test_ssl_connectivity(host: str, port: int = 5432):
    """Test SSL connectivity to the PostgreSQL server."""
    print(f"🔒 Testing SSL connectivity to {host}:{port}...")
    
    try:
        # Create SSL context
        context = ssl.create_default_context()
        
        # Connect with SSL
        with socket.create_connection((host, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=host) as ssock:
                print(f"✅ SSL connection successful")
                print(f"   SSL version: {ssock.version()}")
                print(f"   Cipher: {ssock.cipher()}")
                return True
                
    except Exception as e:
        print(f"❌ SSL connection error: {e}")
        return False


def test_psycopg2_connection(connection_string: str):
    """Test connection using psycopg2 directly."""
    print(f"🐘 Testing psycopg2 connection...")
    
    try:
        import psycopg2
        
        # Test connection
        conn = psycopg2.connect(connection_string)
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        
        print(f"✅ psycopg2 connection successful!")
        print(f"   PostgreSQL version: {version}")
        
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        print("❌ psycopg2 not installed. Install with: pip install psycopg2-binary")
        return False
    except Exception as e:
        print(f"❌ psycopg2 connection error: {e}")
        return False


def test_sqlalchemy_connection():
    """Test connection using SQLAlchemy."""
    print(f"🔧 Testing SQLAlchemy connection...")
    
    try:
        from rainbowplus.api.database import get_database_url, create_engine
        
        # Get the database URL
        db_url = get_database_url()
        print(f"   Connection URL: {db_url.replace(os.environ.get('POSTGRES_PASSWORD', ''), '***')}")
        
        # Create engine and test
        engine = create_engine(db_url)
        with engine.connect() as conn:
            result = conn.execute("SELECT version()")
            version = result.fetchone()[0]
            
            print(f"✅ SQLAlchemy connection successful!")
            print(f"   PostgreSQL version: {version}")
            return True
            
    except Exception as e:
        print(f"❌ SQLAlchemy connection error: {e}")
        return False


def build_connection_strings():
    """Build different connection string formats for testing."""
    
    host = os.environ.get('POSTGRES_HOST', 'deepassuredb.postgres.database.azure.com')
    port = os.environ.get('POSTGRES_PORT', '5432')
    user = os.environ.get('POSTGRES_USER', 'admin@deepassuredb')
    password = os.environ.get('POSTGRES_PASSWORD', 'password')
    database = os.environ.get('POSTGRES_DB', 'postgres')
    
    # URL encode password
    encoded_password = quote_plus(password)
    
    connection_strings = {
        'basic': f"host={host} port={port} dbname={database} user={user} password={password}",
        'ssl_require': f"host={host} port={port} dbname={database} user={user} password={password} sslmode=require",
        'url_basic': f"postgresql://{user}:{encoded_password}@{host}:{port}/{database}",
        'url_ssl': f"postgresql://{user}:{encoded_password}@{host}:{port}/{database}?sslmode=require",
    }
    
    return connection_strings, host, int(port)


def main():
    """Main diagnostic function."""
    print("🚀 Azure PostgreSQL Connection Diagnostics")
    print("   Diagnosing connection to deepassuredb.postgres.database.azure.com")
    print()
    
    # Load environment
    env_file = Path(".env.deepassure")
    if env_file.exists():
        print(f"📁 Loading environment from: {env_file}")
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
    else:
        print("⚠️  No .env.deepassure file found, using environment variables")
    
    print()
    
    # Get connection details
    connection_strings, host, port = build_connection_strings()
    
    print(f"📊 Connection Details:")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   User: {os.environ.get('POSTGRES_USER', 'not set')}")
    print(f"   Database: {os.environ.get('POSTGRES_DB', 'not set')}")
    print(f"   SSL: {os.environ.get('POSTGRES_SSL', 'auto')}")
    print()
    
    # Run diagnostics
    results = []
    
    # Test 1: Basic TCP connectivity
    results.append(("TCP Connectivity", test_basic_connectivity(host, port)))
    
    # Test 2: SSL connectivity
    results.append(("SSL Connectivity", test_ssl_connectivity(host, port)))
    
    # Test 3: psycopg2 connection
    results.append(("psycopg2 (SSL)", test_psycopg2_connection(connection_strings['ssl_require'])))
    
    # Test 4: SQLAlchemy connection
    results.append(("SQLAlchemy", test_sqlalchemy_connection()))
    
    # Summary
    print("\n📊 Diagnostic Results:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    # Recommendations
    print("\n💡 Troubleshooting Tips:")
    
    if not results[0][1]:  # TCP failed
        print("   - Check if the server name is correct")
        print("   - Verify your IP is allowed in Azure PostgreSQL firewall")
        print("   - Check if you're behind a corporate firewall")
    
    if not results[1][1]:  # SSL failed
        print("   - Azure PostgreSQL requires SSL connections")
        print("   - Check if SSL is properly configured")
    
    if not results[2][1] or not results[3][1]:  # Database connection failed
        print("   - Verify username format: admin@servername")
        print("   - Check password is correct")
        print("   - Ensure database name exists")
        print("   - Try connecting with Azure CLI: az postgres flexible-server connect")
    
    print("\n🔧 Next Steps:")
    print("   1. Update .env.deepassure with correct credentials")
    print("   2. Run: python switch_env.py deepassure")
    print("   3. Run: python setup_database.py --env-file .env.deepassure --test-only")


if __name__ == "__main__":
    main()
