#!/usr/bin/env python3
"""
Celery health monitoring script for RainbowPlus.
Can be used as a standalone health check or integrated with monitoring systems.

Usage:
    python celery_health_monitor.py --check-workers
    python celery_health_monitor.py --check-queue
    python celery_health_monitor.py --full-report
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_celery_workers():
    """Check Celery worker status."""
    try:
        from rainbowplus.api.tasks import celery_app
        
        print("🔍 Checking Celery workers...")
        
        # Get worker inspection interface
        inspect = celery_app.control.inspect()
        
        # Check active workers
        active_workers = inspect.active()
        registered_tasks = inspect.registered()
        stats = inspect.stats()
        reserved_tasks = inspect.reserved()
        
        if active_workers is None:
            print("❌ No Celery workers are responding")
            return False
        
        worker_count = len(active_workers)
        print(f"✅ Found {worker_count} active worker(s)")
        
        for worker_name in active_workers.keys():
            print(f"\n📊 Worker: {worker_name}")
            
            # Active tasks
            active_tasks = active_workers.get(worker_name, [])
            print(f"   Active tasks: {len(active_tasks)}")
            
            # Reserved tasks
            reserved = reserved_tasks.get(worker_name, []) if reserved_tasks else []
            print(f"   Reserved tasks: {len(reserved)}")
            
            # Registered tasks
            registered = registered_tasks.get(worker_name, []) if registered_tasks else []
            print(f"   Registered tasks: {len(registered)}")
            
            # Worker stats
            worker_stats = stats.get(worker_name, {}) if stats else {}
            if worker_stats:
                print(f"   Pool: {worker_stats.get('pool', {}).get('max-concurrency', 'N/A')} max concurrency")
                print(f"   Total tasks: {worker_stats.get('total', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Celery workers: {e}")
        return False


def check_celery_queue():
    """Check Celery queue status."""
    try:
        from rainbowplus.api.tasks import celery_app
        import redis
        
        print("🔍 Checking Celery queue...")
        
        # Get Redis connection details
        redis_host = os.environ.get('REDIS_HOST', 'localhost')
        redis_port = int(os.environ.get('REDIS_PORT', '6379'))
        redis_password = os.environ.get('REDIS_PASSWORD')
        
        # Connect to Redis
        redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            password=redis_password,
            decode_responses=True
        )
        
        # Check Redis connectivity
        if not redis_client.ping():
            print("❌ Cannot connect to Redis")
            return False
        
        print("✅ Redis connection successful")
        
        # Check queue lengths
        queue_names = ['celery']  # Default Celery queue
        
        total_pending = 0
        for queue_name in queue_names:
            queue_length = redis_client.llen(queue_name)
            total_pending += queue_length
            print(f"   Queue '{queue_name}': {queue_length} pending tasks")
        
        print(f"📊 Total pending tasks: {total_pending}")
        
        # Check for failed tasks (if using result backend)
        try:
            failed_tasks = redis_client.llen('celery-task-meta-*')
            print(f"📊 Failed tasks: {failed_tasks}")
        except:
            pass  # Result backend might not be configured
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Celery queue: {e}")
        return False


def test_celery_task():
    """Test submitting a simple task to Celery."""
    try:
        from rainbowplus.api.tasks import celery_app
        
        print("🧪 Testing Celery task submission...")
        
        # Submit a simple test task (ping)
        result = celery_app.send_task('celery.ping')
        
        print(f"   Task ID: {result.id}")
        print(f"   Task state: {result.state}")
        
        # Wait for result with timeout
        try:
            task_result = result.get(timeout=10)
            print(f"✅ Task completed successfully: {task_result}")
            return True
        except Exception as e:
            print(f"❌ Task failed or timed out: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Celery task: {e}")
        return False


def get_celery_metrics():
    """Get detailed Celery metrics."""
    try:
        from rainbowplus.api.tasks import celery_app
        
        inspect = celery_app.control.inspect()
        
        # Gather all metrics
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "workers": {
                "active": inspect.active(),
                "registered": inspect.registered(),
                "stats": inspect.stats(),
                "reserved": inspect.reserved(),
                "scheduled": inspect.scheduled(),
                "revoked": inspect.revoked()
            }
        }
        
        return metrics
        
    except Exception as e:
        print(f"❌ Error getting Celery metrics: {e}")
        return None


def full_health_report():
    """Generate a comprehensive Celery health report."""
    print("🚀 Celery Health Report")
    print("=" * 50)
    
    # Check workers
    workers_healthy = check_celery_workers()
    print()
    
    # Check queue
    queue_healthy = check_celery_queue()
    print()
    
    # Test task submission
    task_healthy = test_celery_task()
    print()
    
    # Overall status
    overall_healthy = workers_healthy and queue_healthy and task_healthy
    
    print("📊 Overall Status:")
    print(f"   Workers: {'✅ Healthy' if workers_healthy else '❌ Unhealthy'}")
    print(f"   Queue: {'✅ Healthy' if queue_healthy else '❌ Unhealthy'}")
    print(f"   Task Processing: {'✅ Healthy' if task_healthy else '❌ Unhealthy'}")
    print(f"   Overall: {'✅ Healthy' if overall_healthy else '❌ Unhealthy'}")
    
    return overall_healthy


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Celery Health Monitor for RainbowPlus")
    parser.add_argument("--check-workers", action="store_true", help="Check worker status")
    parser.add_argument("--check-queue", action="store_true", help="Check queue status")
    parser.add_argument("--test-task", action="store_true", help="Test task submission")
    parser.add_argument("--full-report", action="store_true", help="Generate full health report")
    parser.add_argument("--metrics", action="store_true", help="Output metrics as JSON")
    parser.add_argument("--exit-code", action="store_true", help="Exit with non-zero code if unhealthy")
    
    args = parser.parse_args()
    
    if not any([args.check_workers, args.check_queue, args.test_task, args.full_report, args.metrics]):
        # Default to full report
        args.full_report = True
    
    healthy = True
    
    try:
        if args.check_workers:
            healthy = check_celery_workers() and healthy
        
        if args.check_queue:
            healthy = check_celery_queue() and healthy
        
        if args.test_task:
            healthy = test_celery_task() and healthy
        
        if args.full_report:
            healthy = full_health_report() and healthy
        
        if args.metrics:
            metrics = get_celery_metrics()
            if metrics:
                print(json.dumps(metrics, indent=2))
            else:
                healthy = False
    
    except KeyboardInterrupt:
        print("\n⚠️  Health check interrupted")
        healthy = False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        healthy = False
    
    if args.exit_code:
        sys.exit(0 if healthy else 1)
    
    return healthy


if __name__ == "__main__":
    main()
