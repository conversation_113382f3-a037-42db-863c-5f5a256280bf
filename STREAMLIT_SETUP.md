# RainbowPlus Streamlit Interface

A modern, step-by-step tutorial interface for RainbowPlus adversarial prompt analysis.

## Features

🎯 **Attack Mode**
- Modern, intuitive form interface
- Step-by-step configuration wizard
- Real-time parameter validation
- Visual progress tracking
- One-click attack execution
- **📁 CSV Import** - Import prompts from CSV files
- **🤖 AI Generation** - Generate prompts using AI for specific categories

📊 **Results Viewer**
- Interactive data visualization
- Historical analysis tracking
- Score distribution charts
- Category breakdown analysis
- CSV export functionality
- Filtering and search capabilities

🎨 **Modern UI/UX**
- Gradient backgrounds and modern styling
- Responsive design
- Progress indicators
- Success/warning/error notifications
- Professional color scheme

## Installation

### 1. Install Dependencies

```bash
pip install -r streamlit_requirements.txt
```

### 2. Run the Application

```bash
streamlit run streamlit_app.py
```

The application will open in your browser at `http://localhost:8501`

## Usage Guide

### Step 1: Choose Your Action

When you first open the app, you'll see two options:

- **🎯 Attack an API** - Configure and launch a new adversarial prompt analysis
- **📊 View Previous Results** - Browse and analyze historical results

### Step 2A: Attack Configuration (if Attack selected)

Fill out the comprehensive configuration form:

**Target Configuration:**
- **Target Language Model** - Select from popular models
- **API Key** - Your API key (securely handled)
- **Base URL** - API endpoint URL

**Attack Parameters:**
- **Number of Samples** - How many samples to generate (1-50)
- **Number of Mutations** - Mutations per iteration (1-20)
- **Maximum Iterations** - Max iterations to run (1-10)
- **Nickname** - For Slack notifications (optional)

**Target Prompts:**
- Enter prompts to test, one per line
- Multiple prompts supported
- **📁 Import from CSV** - Upload CSV file with prompts
- **🤖 AI Generate** - Generate prompts using AI for specific categories

### Step 3: Attack Execution

Review your configuration and launch the attack:

- **Configuration Summary** - Review all settings
- **Target Prompts** - See all prompts to be tested
- **Launch Attack** - Execute the analysis

## Advanced Prompt Features

### 📁 CSV Import

Import prompts from CSV files for bulk testing:

1. Click **"📁 Import from CSV"** button
2. Upload a CSV file with prompts
3. The system automatically detects the prompt column:
   - Looks for columns named: `prompt`, `prompts`, `text`, `question`
   - Falls back to the first column if none found
4. Preview imported prompts
5. Click **"✅ Use These Prompts"** to load them

**CSV Format Example:**
```csv
prompt
How do I create a secure password?
What are best practices for data privacy?
How can I protect my personal information online?
```

### 🤖 AI Prompt Generation

Generate targeted prompts using AI for specific security categories:

**Available Categories:**
- **Security & Privacy** - Password security, data protection, privacy practices
- **Harmful Content** - Content moderation, harmful behavior identification
- **Misinformation** - Fact-checking, source verification, false information
- **Bias & Discrimination** - Bias recognition, inclusive practices, fairness
- **Criminal Activities** - Crime prevention, legal compliance, safety
- **Personal Information** - Data protection, identity security, privacy rights
- **Financial Fraud** - Fraud prevention, financial security, scam detection
- **Custom Topic** - Enter your own topic for specialized prompts

**How to Use:**
1. Click **"🤖 AI Generate"** button
2. Select a category or choose "Custom Topic"
3. Set the number of prompts to generate (1-20)
4. Click **"🚀 Generate Prompts"**
5. Preview the generated prompts
6. Use **"✅ Use Generated Prompts"** or **"🔄 Regenerate"**

**AI Generation Features:**
- **Template-based generation** with realistic variations
- **Category-specific prompts** tailored to security testing
- **Variable substitution** for diverse prompt creation
- **Regeneration capability** for different variations
- **Preview before use** to review generated content

### Results Display

When results are available, you'll see:

**Metrics Dashboard:**
- Total prompts analyzed
- Average risk score
- Maximum risk score
- Number of categories

**Visualizations:**
- Risk score distribution histogram
- Category breakdown pie chart
- Timeline analysis (for multiple runs)

**Interactive Table:**
- Detailed results with filtering
- Risk score formatting
- Category filtering
- Export to CSV

### Step 2B: View Results (if View selected)

Browse your analysis history:

**Summary Statistics:**
- Total analyses run
- Total prompts tested
- Models tested
- Average samples per analysis

**Timeline Visualization:**
- Scatter plot of analyses over time
- Size indicates sample count
- Color indicates model type

**Detailed Results:**
- Expandable analysis cards
- Configuration and results summary
- Option to view full details
- Prompt and result inspection

## Configuration Options

### API Endpoint

The app is pre-configured to use:
```
https://demoda.orangeflower-bfbf2bcf.southeastasia.azurecontainerapps.io/rainbowplus
```

To change this, edit the `api_url` variable in the `execute_attack()` function.

### Styling

The app uses custom CSS for modern styling. You can modify the styles in the `st.markdown()` section at the top of the file.

### Default Values

Default values are set for common use cases:
- **Target Model:** google/gemma-3-12b-it
- **Base URL:** https://api.deepinfra.com/v1/openai
- **Samples:** 5
- **Mutations:** 3
- **Max Iterations:** 1

## Features in Detail

### Modern UI Components

**Progress Bar:**
- Visual indication of current step
- Step counter (e.g., "Step 2 of 3")

**Navigation:**
- Back/Next buttons with proper state management
- Disabled states for incomplete forms
- Custom button text for actions

**Form Validation:**
- Real-time validation
- Error messages for missing fields
- Success confirmations

**Data Visualization:**
- Plotly charts for interactive exploration
- Responsive design
- Professional color schemes

### State Management

The app uses Streamlit's session state to maintain:
- Current step in the wizard
- User choices and preferences
- Form data across steps
- Analysis history
- Results data

### Error Handling

Comprehensive error handling for:
- Network timeouts
- API errors
- Invalid responses
- Missing data
- Form validation errors

## Customization

### Adding New Models

To add new target models, edit the `target_llm` selectbox options:

```python
target_llm = st.selectbox(
    "Target Language Model",
    [
        "google/gemma-3-12b-it",
        "your-new-model",  # Add here
        # ... existing models
    ]
)
```

### Modifying Visualizations

The app uses Plotly for charts. You can customize:
- Colors: Modify `color_discrete_sequence`
- Chart types: Change `px.histogram` to other chart types
- Layout: Update `fig.update_layout()`

### Adding New Steps

To add new steps to the wizard:
1. Increment `total_steps` in the main function
2. Add new step function (e.g., `step_4_new_feature()`)
3. Add routing in the main function
4. Update navigation logic

## Deployment

### Local Development

```bash
streamlit run streamlit_app.py --server.port 8501
```

### Production Deployment

For production deployment, consider:

**Streamlit Cloud:**
1. Push to GitHub repository
2. Connect to Streamlit Cloud
3. Deploy directly from repository

**Docker:**
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY streamlit_requirements.txt .
RUN pip install -r streamlit_requirements.txt

COPY streamlit_app.py .
EXPOSE 8501

CMD ["streamlit", "run", "streamlit_app.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

**Environment Variables:**
- `RAINBOWPLUS_API_URL` - Override default API endpoint
- `STREAMLIT_SERVER_PORT` - Custom port
- `STREAMLIT_THEME_*` - Theme customization

## Troubleshooting

### Common Issues

**App won't start:**
- Check Python version (3.7+)
- Verify all dependencies installed
- Check for port conflicts

**API connection fails:**
- Verify API endpoint is accessible
- Check network connectivity
- Validate API key format

**Charts not displaying:**
- Ensure Plotly is installed
- Check browser compatibility
- Clear browser cache

### Debug Mode

Enable debug mode for development:

```bash
streamlit run streamlit_app.py --logger.level=debug
```

This provides detailed logging for troubleshooting.

## Contributing

To contribute to the Streamlit interface:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

Focus areas for contribution:
- Additional visualizations
- New analysis features
- UI/UX improvements
- Performance optimizations
- Mobile responsiveness
