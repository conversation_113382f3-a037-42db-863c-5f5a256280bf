# Azure Production Environment Configuration
# Use this for Azure deployment with Azure PostgreSQL

# Database Configuration (Azure PostgreSQL)
POSTGRES_HOST=your-server.postgres.database.azure.com
POSTGRES_PORT=5432
POSTGRES_USER=your_admin_user@your-server
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=rainbowplus
POSTGRES_SSL=true

# Database Performance Settings
DB_ECHO=false
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Redis Configuration (Azure Redis Cache)
REDIS_HOST=your-redis.redis.cache.windows.net
REDIS_PORT=6380
REDIS_PASSWORD=your_redis_key

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Logging
LOG_LEVEL=INFO

# Azure Specific Settings
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=your-resource-group
