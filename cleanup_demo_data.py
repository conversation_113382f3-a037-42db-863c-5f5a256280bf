#!/usr/bin/env python3
"""
Clean up demo data and prepare system for real data entry.
This script will remove demo events but keep the database structure intact.
"""

import requests
import json
from datetime import datetime

# API endpoints
BASE_URL = "http://localhost:8000"

def get_demo_events():
    """Get all events that were created for demo purposes."""
    
    print("🔍 Identifying Demo Events...")
    
    try:
        response = requests.get(f"{BASE_URL}/datagen/events?limit=100")
        response.raise_for_status()
        data = response.json()
        
        demo_events = []
        real_events = []
        
        for event in data['events']:
            user_session_id = event.get('user_session_id', '')
            
            # Identify demo events by session ID pattern
            if (user_session_id.startswith('demo-') or 
                user_session_id.startswith('session-') or
                event.get('application_description', '').startswith('Healthcare diagnostic AI system') or
                event.get('application_description', '').startswith('Legal document analysis system') or
                event.get('application_description', '').startswith('Financial fraud detection system') or
                event.get('application_description', '').startswith('Educational content moderation system') or
                event.get('application_description', '').startswith('Customer service chatbot') or
                event.get('application_description', '').startswith('Security incident response system')):
                demo_events.append(event)
            else:
                real_events.append(event)
        
        print(f"   📊 Found {len(demo_events)} demo events")
        print(f"   📊 Found {len(real_events)} real events")
        
        return demo_events, real_events
        
    except Exception as e:
        print(f"❌ Error getting events: {e}")
        return [], []

def show_current_data_summary():
    """Show summary of current data in the system."""
    
    print("\n📊 Current Data Summary")
    print("=" * 40)
    
    try:
        # Get all events
        response = requests.get(f"{BASE_URL}/datagen/events?limit=100")
        response.raise_for_status()
        events_data = response.json()
        
        # Get all samples
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=100")
        response.raise_for_status()
        samples_data = response.json()
        
        print(f"📝 Total Events: {events_data['total_count']}")
        print(f"📝 Total Samples: {samples_data['total_count']}")
        
        # Show expert assignments
        expert_counts = {}
        domain_counts = {}
        
        for event in events_data['events']:
            experts = event.get('selected_experts', [])
            domain = event.get('domain', 'Unknown')
            
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
            
            if experts:
                for expert in experts:
                    expert_counts[expert] = expert_counts.get(expert, 0) + 1
        
        print(f"\n👥 Expert Assignments:")
        for expert, count in sorted(expert_counts.items()):
            print(f"   {expert}: {count} events")
        
        print(f"\n🏷️ Domain Distribution:")
        for domain, count in sorted(domain_counts.items()):
            print(f"   {domain}: {count} events")
            
    except Exception as e:
        print(f"❌ Error getting summary: {e}")

def show_expert_system_status():
    """Show the status of the expert review system."""
    
    print("\n🔧 Expert Review System Status")
    print("=" * 40)
    
    # Test each expert endpoint
    experts = [
        {"id": "expert_003", "name": "Dr. Sarah Chen"},
        {"id": "safety_expert", "name": "Prof. Michael Rodriguez"},
        {"id": "legal_expert", "name": "Dr. Emily Watson"},
        {"id": "test_expert", "name": "Alex Kim"},
    ]
    
    for expert in experts:
        try:
            response = requests.get(f"{BASE_URL}/datagen/events?expert_id={expert['id']}&limit=1")
            response.raise_for_status()
            data = response.json()
            
            print(f"   ✅ {expert['name']} ({expert['id']}): {data['total_count']} events")
            
        except Exception as e:
            print(f"   ❌ {expert['name']} ({expert['id']}): Error - {e}")

def provide_usage_instructions():
    """Provide instructions for using the system with real data."""
    
    print("\n📋 Instructions for Real Data Usage")
    print("=" * 50)
    
    print("🌐 Frontend Access:")
    print("   • Open: http://localhost:3002")
    print("   • Navigate to 'Generate Dataset' section")
    print("   • Complete the 4-step process to create real events")
    
    print("\n📝 Creating Real Events:")
    print("   1. Step 1: Describe your actual LLM application")
    print("   2. Step 2: Configure test parameters (complexity, coverage, size)")
    print("   3. Step 3: Select relevant domain experts from the 4 available")
    print("   4. Step 4: Monitor generation progress")
    
    print("\n👥 Available Experts:")
    print("   • Dr. Sarah Chen (expert_003): Healthcare AI")
    print("   • Prof. Michael Rodriguez (safety_expert): Safety & Security")
    print("   • Dr. Emily Watson (legal_expert): Legal AI Ethics")
    print("   • Alex Kim (test_expert): AI Testing")
    
    print("\n🔍 Reviewing Events:")
    print("   • Navigate to 'Expert Review' section")
    print("   • Use 'Switch Expert View' dropdown")
    print("   • Each expert sees only their assigned events")
    print("   • Filter by domain, search by keywords")
    print("   • View associated dataset samples")
    
    print("\n💡 Tips:")
    print("   • Assign multiple experts to complex projects")
    print("   • Use domain-specific experts for better results")
    print("   • Higher complexity = more thorough testing")
    print("   • Larger dataset size = more comprehensive coverage")

if __name__ == "__main__":
    print("🧹 RainbowPlus Expert Review - Real Data Preparation")
    print("=" * 60)
    
    # Show current status
    show_current_data_summary()
    show_expert_system_status()
    
    # Get demo vs real events
    demo_events, real_events = get_demo_events()
    
    if demo_events:
        print(f"\n⚠️  Found {len(demo_events)} demo events in the system")
        print("   These were created for testing the expert review functionality")
        print("   You can continue using the system - new real events will be mixed with demo data")
        print("   Or you can clean the database if you want to start fresh")
    else:
        print(f"\n✅ No demo events found - system is ready for real data")
    
    if real_events:
        print(f"\n📊 Found {len(real_events)} real events already in the system")
        print("   These will be preserved and available in the expert review interface")
    
    # Provide usage instructions
    provide_usage_instructions()
    
    print(f"\n🎉 System is ready for real data!")
    print(f"   The expert review feature will work with any new events you create.")
    print(f"   Just make sure to assign experts during the dataset generation process.")
