#!/usr/bin/env python3
"""
Verify that the expert review system is working correctly with real data.
"""

import requests
import json

# API endpoints
BASE_URL = "http://localhost:8000"

def test_expert_review_with_real_data():
    """Test the expert review system with current real data."""
    
    print("🧪 Testing Expert Review System with Real Data")
    print("=" * 60)
    
    # The 4 experts that are hardcoded in the frontend
    experts = [
        {"id": "expert_003", "name": "Dr. <PERSON>", "specialty": "Healthcare AI"},
        {"id": "safety_expert", "name": "Prof<PERSON>", "specialty": "Safety & Security"},
        {"id": "legal_expert", "name": "<PERSON><PERSON>", "specialty": "Legal AI Ethics"},
        {"id": "test_expert", "name": "<PERSON>", "specialty": "AI Testing"},
    ]
    
    total_assigned_events = 0
    
    for expert in experts:
        print(f"\n👤 {expert['name']} ({expert['specialty']})")
        print("-" * 50)
        
        try:
            # Get events for this expert
            response = requests.get(f"{BASE_URL}/datagen/events?expert_id={expert['id']}")
            response.raise_for_status()
            data = response.json()
            
            events = data['events']
            event_count = data['total_count']
            total_assigned_events += event_count
            
            print(f"   📊 Assigned Events: {event_count}")
            
            if events:
                # Analyze domains
                domains = set()
                complexities = []
                dataset_sizes = []
                
                for event in events:
                    if event.get('domain'):
                        domains.add(event['domain'])
                    if event.get('complexity'):
                        complexities.append(event['complexity'])
                    if event.get('dataset_size'):
                        dataset_sizes.append(event['dataset_size'])
                
                print(f"   🏷️ Domains: {', '.join(sorted(domains)) if domains else 'None'}")
                
                if complexities:
                    avg_complexity = sum(complexities) / len(complexities)
                    print(f"   📈 Avg Complexity: {avg_complexity:.1f}/10")
                
                if dataset_sizes:
                    total_samples = sum(dataset_sizes)
                    print(f"   📝 Total Dataset Size: {total_samples:,}")
                
                # Show recent events
                print(f"   📋 Recent Events:")
                for event in events[:3]:
                    desc = event.get('application_description', 'No description')[:60]
                    domain = event.get('domain', 'Unknown')
                    project_name = event.get('project_name', 'Unnamed')
                    print(f"      • [{domain}] {project_name}: {desc}...")
            else:
                print(f"   📝 No events currently assigned")
                print(f"   💡 Create new events and assign this expert to see them here")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Summary")
    print("=" * 30)
    print(f"Total events assigned to the 4 main experts: {total_assigned_events}")
    
    return total_assigned_events > 0

def test_dataset_generation_flow():
    """Test that the dataset generation flow works with expert assignment."""
    
    print(f"\n🔄 Testing Dataset Generation Flow")
    print("=" * 40)
    
    # Test the datagen events endpoint
    try:
        response = requests.get(f"{BASE_URL}/datagen/events?limit=1")
        response.raise_for_status()
        print("   ✅ Datagen events API working")
        
        # Test dataset samples endpoint
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=1")
        response.raise_for_status()
        print("   ✅ Dataset samples API working")
        
        # Test project listing (needed for dataset generation)
        response = requests.get(f"{BASE_URL}/projects")
        response.raise_for_status()
        projects_data = response.json()
        print(f"   ✅ Projects API working ({projects_data['total_count']} projects available)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API test failed: {e}")
        return False

def test_frontend_integration():
    """Test that the frontend can access the APIs."""
    
    print(f"\n🌐 Testing Frontend Integration")
    print("=" * 40)
    
    try:
        # Test if frontend is running
        response = requests.get("http://localhost:3002", timeout=5)
        if response.status_code == 200:
            print("   ✅ Frontend server running on port 3002")
        else:
            print(f"   ⚠️ Frontend server responded with status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️ Frontend server not running on port 3002")
        print("   💡 Start with: cd client && PORT=3002 npm run dev")
    except Exception as e:
        print(f"   ❌ Frontend test failed: {e}")

def provide_next_steps():
    """Provide next steps for using the system with real data."""
    
    print(f"\n📋 Next Steps for Real Data Usage")
    print("=" * 50)
    
    print("🎯 To Create Real Dataset Generation Events:")
    print("   1. Open http://localhost:3002")
    print("   2. Navigate to 'Generate Dataset' section")
    print("   3. Fill out Step 1: Application Description")
    print("      • Describe your actual LLM application")
    print("      • Specify the domain (healthcare, legal, finance, etc.)")
    print("      • Provide a realistic example input")
    
    print("\n   4. Configure Step 2: Test Configuration")
    print("      • Set complexity (1-10) based on your needs")
    print("      • Set coverage percentage (how thorough)")
    print("      • Set dataset size (number of samples)")
    
    print("\n   5. Select Step 3: Expert Collaboration")
    print("      • Choose relevant experts:")
    print("        - Dr. Sarah Chen: Healthcare AI")
    print("        - Prof. Michael Rodriguez: Safety & Security")
    print("        - Dr. Emily Watson: Legal AI Ethics")
    print("        - Alex Kim: AI Testing")
    print("      • You can select multiple experts for complex projects")
    
    print("\n   6. Monitor Step 4: Generation Progress")
    print("      • Watch the dataset generation process")
    print("      • Events and samples will be saved to database")
    
    print("\n🔍 To Review Generated Data:")
    print("   1. Navigate to 'Expert Review' section")
    print("   2. Use 'Switch Expert View' dropdown")
    print("   3. Each expert sees only their assigned events")
    print("   4. Use filters to find specific domains or projects")
    print("   5. View dataset samples linked to each event")
    
    print("\n💡 Best Practices:")
    print("   • Assign domain-specific experts for better results")
    print("   • Use multiple experts for complex or sensitive applications")
    print("   • Higher complexity settings provide more thorough testing")
    print("   • Larger dataset sizes give more comprehensive coverage")

if __name__ == "__main__":
    print("🔧 RainbowPlus Expert Review - Real Data System Verification")
    print("=" * 70)
    
    # Test the system
    has_data = test_expert_review_with_real_data()
    api_working = test_dataset_generation_flow()
    test_frontend_integration()
    
    if api_working:
        print(f"\n✅ System Status: READY FOR REAL DATA")
        
        if has_data:
            print(f"   📊 Current data: Available for expert review")
        else:
            print(f"   📝 Current data: No events assigned to main experts yet")
            print(f"   💡 Create new events to see expert review functionality")
            
    else:
        print(f"\n❌ System Status: API ISSUES DETECTED")
        print(f"   🔧 Please check backend server and database connectivity")
    
    provide_next_steps()
    
    print(f"\n🎉 Expert Review System Ready!")
    print(f"   The system will work with any real data you create through the interface.")
    print(f"   Expert assignments made during dataset generation will appear in expert review.")
