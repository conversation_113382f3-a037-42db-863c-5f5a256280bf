#!/usr/bin/env python3
"""
Test script to verify the expert dropdown demo functionality.
"""

import requests
import json

# API endpoints
BASE_URL = "http://localhost:8000"

def test_expert_dropdown_demo():
    """Test that each expert in the dropdown has different data."""
    
    print("🧪 Testing Expert Dropdown Demo Functionality")
    print("=" * 60)
    
    # These are the experts available in the dropdown
    experts = [
        {"id": "expert_003", "name": "Dr. <PERSON>", "specialty": "Healthcare AI"},
        {"id": "safety_expert", "name": "Prof<PERSON> <PERSON>", "specialty": "Safety & Security"},
        {"id": "legal_expert", "name": "Dr<PERSON>", "specialty": "Legal AI Ethics"},
        {"id": "test_expert", "name": "<PERSON>", "specialty": "AI Testing"},
    ]
    
    expert_data = {}
    
    for expert in experts:
        expert_id = expert["id"]
        expert_name = expert["name"]
        
        print(f"\n👤 Testing {expert_name} ({expert_id})")
        print("-" * 50)
        
        try:
            # Get events for this expert
            response = requests.get(f"{BASE_URL}/datagen/events?expert_id={expert_id}")
            response.raise_for_status()
            data = response.json()
            
            events = data['events']
            total_events = data['total_count']
            
            # Analyze the data
            domains = set()
            complexities = []
            dataset_sizes = []
            
            for event in events:
                if event.get('domain'):
                    domains.add(event['domain'])
                if event.get('complexity'):
                    complexities.append(event['complexity'])
                if event.get('dataset_size'):
                    dataset_sizes.append(event['dataset_size'])
            
            # Calculate statistics
            avg_complexity = sum(complexities) / len(complexities) if complexities else 0
            total_samples = sum(dataset_sizes) if dataset_sizes else 0
            
            expert_data[expert_id] = {
                "name": expert_name,
                "specialty": expert["specialty"],
                "total_events": total_events,
                "domains": list(domains),
                "avg_complexity": round(avg_complexity, 1),
                "total_samples": total_samples
            }
            
            print(f"   📊 Events Assigned: {total_events}")
            print(f"   🏷️ Domains: {', '.join(sorted(domains)) if domains else 'None'}")
            print(f"   📈 Avg Complexity: {avg_complexity:.1f}/10")
            print(f"   📝 Total Dataset Samples: {total_samples:,}")
            
            if events:
                print(f"   📋 Recent Events:")
                for event in events[:3]:  # Show first 3 events
                    desc = event.get('application_description', '')[:60]
                    domain = event.get('domain', 'Unknown')
                    complexity = event.get('complexity', 0)
                    print(f"      • [{domain}] {desc}... (Complexity: {complexity})")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            expert_data[expert_id] = {"error": str(e)}
    
    # Summary comparison
    print(f"\n📊 Expert Comparison Summary")
    print("=" * 60)
    
    print(f"{'Expert':<25} {'Events':<8} {'Domains':<12} {'Complexity':<12} {'Samples':<10}")
    print("-" * 75)
    
    for expert_id, data in expert_data.items():
        if "error" not in data:
            name = data["name"][:24]
            events = data["total_events"]
            domains = len(data["domains"])
            complexity = data["avg_complexity"]
            samples = data["total_samples"]
            
            print(f"{name:<25} {events:<8} {domains:<12} {complexity:<12} {samples:<10,}")
    
    # Test domain filtering for one expert
    print(f"\n🔍 Testing Domain Filtering (safety_expert)")
    print("-" * 40)
    
    try:
        # Get all domains for safety_expert
        response = requests.get(f"{BASE_URL}/datagen/events?expert_id=safety_expert")
        response.raise_for_status()
        all_events = response.json()['events']
        
        domains_found = set(event.get('domain') for event in all_events if event.get('domain'))
        
        for domain in sorted(domains_found):
            response = requests.get(f"{BASE_URL}/datagen/events?expert_id=safety_expert&domain={domain}")
            response.raise_for_status()
            filtered_data = response.json()
            
            print(f"   {domain}: {filtered_data['total_count']} events")
            
    except Exception as e:
        print(f"   ❌ Domain filtering test failed: {e}")
    
    return expert_data

def test_dataset_samples_correlation():
    """Test that dataset samples are properly correlated with expert assignments."""
    
    print(f"\n🔗 Testing Dataset Samples Correlation")
    print("-" * 40)
    
    try:
        # Get all samples
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=10")
        response.raise_for_status()
        samples_data = response.json()
        
        print(f"📝 Total Dataset Samples Available: {samples_data['total_count']}")
        
        if samples_data['samples']:
            sample = samples_data['samples'][0]
            print(f"📋 Sample Preview:")
            print(f"   ID: {sample['id']}")
            print(f"   Label: {sample.get('label', 'No label')}")
            print(f"   Content: {sample['content'][:100]}...")
            print(f"   Event ID: {sample.get('datagen_event_id', 'Not linked')}")
        
    except Exception as e:
        print(f"❌ Samples correlation test failed: {e}")

if __name__ == "__main__":
    expert_data = test_expert_dropdown_demo()
    test_dataset_samples_correlation()
    
    print(f"\n🎉 Expert Dropdown Demo Test Complete!")
    print(f"\n🌐 Demo Instructions:")
    print("1. Open http://localhost:3002")
    print("2. Click 'Expert Review' in the sidebar")
    print("3. Use the 'Demo: Switch Expert' dropdown to see different views")
    print("4. Notice how each expert sees different:")
    print("   • Number of assigned events")
    print("   • Domain distribution")
    print("   • Average complexity")
    print("   • Total dataset samples")
    print("5. Try filtering by domain to see expert specializations")
    
    # Show which expert has the most interesting data
    if expert_data:
        max_events = max((data.get('total_events', 0) for data in expert_data.values() if 'error' not in data), default=0)
        for expert_id, data in expert_data.items():
            if 'error' not in data and data.get('total_events', 0) == max_events:
                print(f"\n💡 Try '{data['name']}' first - they have the most events ({max_events}) to review!")
                break
