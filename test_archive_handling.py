#!/usr/bin/env python3
"""
Test script to verify Archive object handling in score processing.
This tests the fix for the 'Archive' object has no attribute 'items' error.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rainbowplus.archive import Archive
from rainbowplus.api.tasks import make_json_serializable
from rainbowplus.api.notifications import SlackNotificationService


def create_test_archive():
    """Create a test Archive object similar to what run_rainbowplus returns."""
    
    print("🏗️  Creating test Archive object...")
    
    # Create a scores Archive similar to what run_rainbowplus produces
    scores = Archive("scores")
    
    # Add some test data with tuple keys (like the real system)
    scores.add(('Criminal Planning', 'Slang'), [0.00123, 0.00234, 0.00345])
    scores.add(('Fraud and Scams', 'Hypotheticals'), [0.00456, 0.00567, 0.00678])
    scores.add(('Malware', 'Technical'), [0.00789, 0.00890, 0.00901])
    
    print(f"   ✅ Created Archive with {len(scores.keys())} categories")
    print(f"   Sample keys: {scores.keys()[:2]}")
    
    return scores


def test_archive_to_dict_conversion():
    """Test converting Archive to dictionary for JSON serialization."""
    
    print("\n🧪 Testing Archive to dictionary conversion...")
    
    scores = create_test_archive()
    
    try:
        # This is what we do in the fixed code
        scores_dict = {str(key): value for key, value in scores._archive.items()}
        
        print(f"   ✅ Archive converted to dict with {len(scores_dict)} entries")
        print(f"   Sample converted key: {list(scores_dict.keys())[0]}")
        print(f"   Sample values: {list(scores_dict.values())[0]}")
        
        return scores_dict
        
    except Exception as e:
        print(f"   ❌ Archive conversion failed: {e}")
        return None


def test_json_serialization_with_archive():
    """Test that the converted Archive can be JSON serialized."""
    
    print("\n🧪 Testing JSON serialization with converted Archive...")
    
    scores_dict = test_archive_to_dict_conversion()
    if not scores_dict:
        return False
    
    try:
        # Test the make_json_serializable function
        serializable_scores = make_json_serializable(scores_dict)
        
        import json
        json_str = json.dumps(serializable_scores)
        
        print(f"   ✅ JSON serialization succeeded ({len(json_str)} characters)")
        return True
        
    except Exception as e:
        print(f"   ❌ JSON serialization failed: {e}")
        return False


def test_slack_notification_with_archive():
    """Test that Slack notifications work with converted Archive data."""
    
    print("\n📱 Testing Slack notification with Archive data...")
    
    scores_dict = test_archive_to_dict_conversion()
    if not scores_dict:
        return False
    
    try:
        service = SlackNotificationService()
        
        # Test the score statistics calculation
        score_stats = service._calculate_score_statistics(scores_dict)
        
        print(f"   ✅ Score statistics calculated successfully")
        print(f"   Sample output:")
        for line in score_stats.split('\n')[:3]:  # Show first 3 lines
            print(f"      {line}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Slack notification test failed: {e}")
        return False


def test_complete_workflow():
    """Test the complete workflow from Archive to notification."""
    
    print("\n🔄 Testing complete workflow...")
    
    try:
        # Step 1: Create Archive (simulates run_rainbowplus output)
        scores = create_test_archive()
        
        # Step 2: Convert to dict (simulates what we do in tasks.py)
        scores_dict = {str(key): value for key, value in scores._archive.items()}
        
        # Step 3: Make JSON serializable (for database storage)
        serializable_result = make_json_serializable({
            "success": True,
            "scores": scores_dict,
            "message": "Test completed"
        })
        
        # Step 4: Test notification (simulates Slack notification)
        service = SlackNotificationService()
        score_stats = service._calculate_score_statistics(serializable_result['scores'])
        
        print(f"   ✅ Complete workflow succeeded!")
        print(f"   Final score statistics preview:")
        print(f"      {score_stats[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Complete workflow failed: {e}")
        return False


def main():
    """Run all Archive handling tests."""
    print("🚀 Archive Handling Tests for RainbowPlus")
    print("   Testing the fix for 'Archive' object has no attribute 'items'...")
    print()
    
    test1_passed = test_archive_to_dict_conversion() is not None
    test2_passed = test_json_serialization_with_archive()
    test3_passed = test_slack_notification_with_archive()
    test4_passed = test_complete_workflow()
    
    print("\n📊 Test Results:")
    print(f"   Archive to dict conversion: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   JSON serialization: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Slack notification: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    print(f"   Complete workflow: {'✅ PASS' if test4_passed else '❌ FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed, test4_passed]):
        print("\n🎉 All tests passed!")
        print("   The Archive handling error should be resolved.")
        print("   Score results will now be included in Slack notifications.")
        return True
    else:
        print("\n❌ Some tests failed.")
        print("   Please check the error messages above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
