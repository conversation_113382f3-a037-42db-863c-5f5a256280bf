# LLM Configuration Guide

This guide explains how to configure different types of Language Models (LLMs) in the RainbowPlus system, including support for both OpenAI and Azure OpenAI APIs.

## Overview

The system supports flexible configuration for different LLM components:
- **target_llm**: The model being tested/attacked
- **mutator_llm**: Generates variations of prompts
- **judge_llm**: Evaluates and compares responses
- **fitness_llm**: Scores the safety/fitness of responses

Each component can be configured to use either:
- Regular OpenAI API (including compatible APIs like DeepInfra, Together.ai, etc.)
- Azure OpenAI API

## Configuration Types

### OpenAI API Configuration

```yaml
component_llm:
  type_: openai
  api_key: your-api-key-here
  base_url: https://api.openai.com/v1  # or compatible endpoint
  model_kwargs:
    model: gpt-3.5-turbo
    max_model_len: 4096
  sampling_params:
    temperature: 0.7
    top_p: 0.9
    max_completion_tokens: 1024
```

### Azure OpenAI Configuration

```yaml
component_llm:
  type_: azure_openai
  api_key: your-azure-api-key-here
  azure_endpoint: https://your-resource.openai.azure.com/
  api_version: 2024-02-15-preview
  model_kwargs:
    model: gpt-4  # deployment name in Azure
    max_model_len: 4096
  sampling_params:
    temperature: 0.7
    top_p: 0.9
    max_completion_tokens: 1024
```

## Required Parameters

### For OpenAI API (`type_: openai`)
- `api_key`: Your API key
- `base_url`: API endpoint URL
- `model_kwargs.model`: Model name

### For Azure OpenAI (`type_: azure_openai`)
- `api_key`: Your Azure API key
- `azure_endpoint`: Your Azure OpenAI resource endpoint
- `api_version`: API version (e.g., "2024-02-15-preview")
- `model_kwargs.model`: Deployment name in Azure

## Mixed Configuration Example

You can mix different API types for different components:

```yaml
# Use regular OpenAI for target model
target_llm:
  type_: openai
  api_key: sk-...
  base_url: https://api.openai.com/v1
  model_kwargs:
    model: gpt-3.5-turbo

# Use Azure OpenAI for judge model
judge_llm:
  type_: azure_openai
  api_key: your-azure-key
  azure_endpoint: https://your-resource.openai.azure.com/
  api_version: 2024-02-15-preview
  model_kwargs:
    model: gpt-4

# Use third-party API for fitness model
fitness_llm:
  type_: openai
  api_key: your-together-key
  base_url: https://api.together.xyz/v1
  model_kwargs:
    model: meta-llama/Llama-3.3-70B-Instruct-Turbo-Free
```

## Special Considerations

### Fitness LLM
The fitness_llm requires `logprobs: True` and `top_logprobs: 1` in sampling_params for safety scoring:

```yaml
fitness_llm:
  # ... other config ...
  sampling_params:
    temperature: 0.8
    logprobs: True
    top_logprobs: 1
```

### Judge LLM
The judge_llm is automatically handled by the LLMSwitcher and supports both OpenAI and Azure OpenAI configurations seamlessly.

## Error Handling

The system will automatically:
1. Validate required parameters for each API type
2. Create appropriate client instances (OpenAI vs AzureOpenAI)
3. Provide clear error messages for missing or invalid configurations

Common errors:
- Missing `azure_endpoint` for Azure OpenAI: Use `azure_endpoint` instead of `base_url`
- Missing `base_url` for OpenAI: Required for regular OpenAI API
- Invalid `type_`: Must be either "openai" or "azure_openai"

## Migration from Hardcoded Azure

If you were previously using hardcoded Azure configurations, update your config files:

**Before:**
```yaml
fitness_llm:
  # Azure config but type was wrong
  type_: openai
  api_key: key
  # Missing azure_endpoint
```

**After:**
```yaml
fitness_llm:
  type_: azure_openai  # Correct type
  api_key: key
  azure_endpoint: https://your-resource.openai.azure.com/
  api_version: 2024-02-15-preview
```
