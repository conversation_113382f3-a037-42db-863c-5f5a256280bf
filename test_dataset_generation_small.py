#!/usr/bin/env python3
"""
Test dataset generation with smaller token limits to avoid API issues.
"""

import requests
import json
import time

# API endpoints
BASE_URL = "http://localhost:8000"
DATAGEN_EVENT_URL = f"{BASE_URL}/datagen/events"
DATASET_GENERATION_URL = f"{BASE_URL}/datasets/generate"

def test_small_dataset_generation():
    """Test dataset generation with small token limits."""
    
    print("🧪 Testing Small Dataset Generation")
    print("=" * 50)
    
    # Step 1: Create a datagen event
    print("📝 Step 1: Creating datagen event...")
    
    datagen_event_data = {
        "application_description": "Small test for incident reporting",
        "domain": "Testing",
        "example_input": "Brief incident report",
        "test_types": [
            {"id": "harassment", "label": "Harassment", "checked": True}
        ],
        "complexity": 3,
        "coverage": 50,
        "dataset_size": 5,  # Very small
        "selected_experts": ["test_expert"],
        "uploaded_files": [],
        "user_session_id": "test-session-small-123",
        "project_id": None
    }
    
    try:
        event_response = requests.post(DATAGEN_EVENT_URL, json=datagen_event_data)
        event_response.raise_for_status()
        event_result = event_response.json()
        print(f"✅ Datagen event created: {event_result}")
        event_id = event_result.get('event_id')
    except Exception as e:
        print(f"❌ Failed to create datagen event: {e}")
        return False
    
    # Step 2: Generate dataset with small limits
    print("\n🚀 Step 2: Generating small dataset...")
    
    # Use the API key from base.yml with very conservative limits
    api_key = "********************************************************************************************************************************************************************"
    
    dataset_generation_data = {
        "description": "Small test for incident reporting",
        "examples": ["Brief incident report"],
        "task": "Content",
        "llm": {
            "provider": "openai",
            "api_key": api_key,
            "model_kwargs": {
                "model": "gpt-4o-mini"
            },
            "sampling_params": {
                "temperature": 0.7,
                "max_tokens": 500,  # Much smaller limit
                "top_p": 0.9,
                "n": 1  # Generate only 1 sample
            }
        },
        "nickname": "small-test-user",
        "project_id": None
    }
    
    try:
        dataset_response = requests.post(DATASET_GENERATION_URL, json=dataset_generation_data)
        dataset_response.raise_for_status()
        dataset_result = dataset_response.json()
        print(f"✅ Dataset generation started: {dataset_result}")
        job_id = dataset_result.get('job_id')
    except Exception as e:
        print(f"❌ Failed to start dataset generation: {e}")
        return False
    
    # Step 3: Wait for completion
    print(f"\n⏳ Step 3: Waiting for completion (job_id: {job_id})...")
    
    # Wait for the generation to complete
    for i in range(6):  # Wait up to 30 seconds
        time.sleep(5)
        
        try:
            from rainbowplus.api.database import get_db_session
            from rainbowplus.api.models import RainbowAnalysisJob, GeneratedDataset
            
            with get_db_session() as session:
                job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
                if job:
                    print(f"   Job status: {job.status}")
                    
                    if job.status == 'completed':
                        print("✅ Job completed successfully!")
                        break
                    elif job.status == 'failed':
                        print(f"❌ Job failed: {job.results}")
                        return False
                else:
                    print("❌ Job not found")
                    return False
        except Exception as e:
            print(f"❌ Error checking job status: {e}")
            return False
    
    # Step 4: Check final results
    print("\n🔍 Step 4: Checking final results...")
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import RainbowAnalysisJob, GeneratedDataset
        
        with get_db_session() as session:
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
            if job:
                print(f"📊 Final job status: {job.status}")
                print(f"📊 Job has results: {bool(job.results)}")
                
                if job.results and isinstance(job.results, dict) and 'error' in job.results:
                    print(f"📊 Job error: {job.results['error']}")
                elif job.results:
                    print(f"📊 Results size: {len(str(job.results))} chars")
                
                # Check for GeneratedDataset
                generated_dataset = session.query(GeneratedDataset).filter(
                    GeneratedDataset.job_id == job_id
                ).first()
                
                if generated_dataset:
                    print(f"🎉 SUCCESS: GeneratedDataset found!")
                    print(f"   ID: {generated_dataset.id}")
                    print(f"   Status: {generated_dataset.generation_status}")
                    print(f"   Dataset name: {generated_dataset.dataset_name}")
                    print(f"   Total samples: {generated_dataset.total_samples}")
                    print(f"   Sample groups: {generated_dataset.sample_groups}")
                    print(f"   Datagen event ID: {generated_dataset.datagen_event_id}")
                    print(f"   Created: {generated_dataset.created_at}")
                    print(f"   Completed: {generated_dataset.completed_at}")
                    
                    if generated_dataset.dataset_content:
                        print(f"   Dataset content size: {len(str(generated_dataset.dataset_content))} chars")
                        print(f"   Content preview: {str(generated_dataset.dataset_content)[:150]}...")
                    
                    return True
                else:
                    print("❌ No GeneratedDataset found for this job")
                    print("💡 The new save method may not be working correctly")
                    return False
            else:
                print(f"❌ Job {job_id} not found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking final results: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 Small Dataset Generation Test")
    print("Testing with conservative token limits")
    print()
    
    success = test_small_dataset_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test successful!")
        print("✅ GeneratedDataset table is working correctly")
        print("✅ Pipeline save method is functioning")
    else:
        print("❌ Test failed!")
        print("💡 Check server logs for more details")
    
    return success

if __name__ == "__main__":
    main()
