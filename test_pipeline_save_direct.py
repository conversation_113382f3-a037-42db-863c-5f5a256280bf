#!/usr/bin/env python3
"""
Direct test of the pipeline save functionality by bypassing LLM generation.
This test directly calls the _save_generated_dataset_to_db method to verify it works.
"""

import uuid
from datetime import datetime, timezone

def test_pipeline_save_functionality():
    """Test the pipeline save functionality directly."""
    
    print("🧪 Testing Pipeline Save Functionality (Direct)")
    print("=" * 55)
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import Project, DatagenEvent, RainbowAnalysisJob, GeneratedDataset
        from data_preparation.core.pipeline import Pipeline
        from data_preparation.core.config.base import Config, LLMConfig
        
        # Create test data
        test_project_id = uuid.uuid4()
        test_datagen_event_id = uuid.uuid4()
        test_job_id = uuid.uuid4()
        
        print("📝 Step 1: Creating test data in database...")
        
        with get_db_session() as session:
            # Create test project
            test_project = Project(
                id=test_project_id,
                name="Pipeline Save Test Project",
                description="Test project for pipeline save functionality",
                domain="Testing"
            )
            session.add(test_project)
            
            # Create test datagen event
            test_datagen_event = DatagenEvent(
                id=test_datagen_event_id,
                application_description="Pipeline save test application",
                domain="Testing",
                complexity=3,
                coverage=50,
                dataset_size=10,
                project_id=test_project_id,
                user_session_id="test-pipeline-save-123"
            )
            session.add(test_datagen_event)
            
            # Create test job
            test_job = RainbowAnalysisJob(
                id=test_job_id,
                prompts=["Test prompt for pipeline save"],
                target_llm="test-model",
                num_samples=1,
                num_mutations=1,
                max_iters=1,
                project_id=test_project_id,
                status='running'
            )
            session.add(test_job)
            session.commit()
            
        print(f"✅ Created test data:")
        print(f"   Project ID: {test_project_id}")
        print(f"   Datagen Event ID: {test_datagen_event_id}")
        print(f"   Job ID: {test_job_id}")
        
        print("\n📝 Step 2: Creating mock pipeline instance...")
        
        # Create a minimal config for the pipeline
        mock_config = Config(
            description="test description",
            examples=["test example"],
            documents=["test document"],
            task="Content",
            llm=LLMConfig(provider="test", api_key="test")
        )
        
        # Create pipeline instance (we'll only use the save method)
        pipeline = Pipeline.__new__(Pipeline)  # Create without calling __init__
        pipeline.db_session_factory = get_db_session
        
        print("✅ Mock pipeline instance created")
        
        print("\n📝 Step 3: Creating mock dataset content...")
        
        # Create mock dataset content similar to samples.json
        mock_dataset_content = [
            {
                "samples": [
                    {
                        "type": "content",
                        "content": "**Incident Report**\n**Date:** April 15, 2025\n**Time:** 2:30 PM\n**Location:** Test Park\n**Main Type:** Harassment\n\nThis is a test incident report generated by the pipeline save test.",
                        "label": "Harassment"
                    },
                    {
                        "type": "content",
                        "content": "**Incident Report**\n**Date:** March 25, 2025\n**Time:** 7:45 PM\n**Location:** Test Street\n**Main Type:** Cheating\n\nThis is another test incident report for pipeline save testing.",
                        "label": "Cheating"
                    }
                ]
            }
        ]
        
        print(f"✅ Mock dataset content created with {len(mock_dataset_content[0]['samples'])} samples")
        
        print("\n📝 Step 4: Testing _save_generated_dataset_to_db method...")
        
        # Call the save method directly
        pipeline._save_generated_dataset_to_db(str(test_job_id), mock_dataset_content)
        
        print("✅ _save_generated_dataset_to_db method completed without errors")
        
        print("\n📝 Step 5: Verifying saved data...")
        
        with get_db_session() as session:
            # Check if GeneratedDataset was created
            generated_dataset = session.query(GeneratedDataset).filter(
                GeneratedDataset.job_id == test_job_id
            ).first()
            
            if generated_dataset:
                print(f"🎉 SUCCESS: GeneratedDataset created!")
                print(f"   ID: {generated_dataset.id}")
                print(f"   Status: {generated_dataset.generation_status}")
                print(f"   Dataset name: {generated_dataset.dataset_name}")
                print(f"   Total samples: {generated_dataset.total_samples}")
                print(f"   Sample groups: {generated_dataset.sample_groups}")
                print(f"   Complexity: {generated_dataset.complexity}")
                print(f"   Coverage: {generated_dataset.coverage}")
                print(f"   Dataset size: {generated_dataset.dataset_size}")
                print(f"   Created: {generated_dataset.created_at}")
                print(f"   Completed: {generated_dataset.completed_at}")
                
                # Verify relationships
                print(f"\n📊 Relationship verification:")
                print(f"   Datagen Event ID: {generated_dataset.datagen_event_id}")
                print(f"   Project ID: {generated_dataset.project_id}")
                print(f"   Job ID: {generated_dataset.job_id}")
                
                if generated_dataset.datagen_event_id == test_datagen_event_id:
                    print("   ✅ Correctly linked to datagen event")
                else:
                    print("   ❌ Not linked to datagen event")
                    
                if generated_dataset.project_id == test_project_id:
                    print("   ✅ Correctly linked to project")
                else:
                    print("   ❌ Not linked to project")
                    
                if generated_dataset.job_id == test_job_id:
                    print("   ✅ Correctly linked to job")
                else:
                    print("   ❌ Not linked to job")
                
                # Verify dataset content
                if generated_dataset.dataset_content:
                    print(f"\n📄 Dataset content verification:")
                    print(f"   Content type: {type(generated_dataset.dataset_content)}")
                    print(f"   Content size: {len(str(generated_dataset.dataset_content))} chars")
                    
                    if isinstance(generated_dataset.dataset_content, list) and len(generated_dataset.dataset_content) > 0:
                        first_group = generated_dataset.dataset_content[0]
                        if 'samples' in first_group:
                            print(f"   Samples in first group: {len(first_group['samples'])}")
                            first_sample = first_group['samples'][0]
                            print(f"   First sample type: {first_sample.get('type', 'N/A')}")
                            print(f"   First sample label: {first_sample.get('label', 'N/A')}")
                            print(f"   Content preview: {first_sample.get('content', '')[:100]}...")
                            print("   ✅ Dataset content structure is correct")
                        else:
                            print("   ❌ Dataset content missing 'samples' key")
                    else:
                        print("   ❌ Dataset content is not in expected format")
                else:
                    print("   ❌ No dataset content found")
                
                # Check job status update
                updated_job = session.query(RainbowAnalysisJob).filter(
                    RainbowAnalysisJob.id == test_job_id
                ).first()
                
                if updated_job and updated_job.status == 'completed':
                    print(f"\n✅ Job status updated to 'completed'")
                else:
                    print(f"\n❌ Job status not updated (current: {updated_job.status if updated_job else 'Not found'})")
                
                print("\n📝 Step 6: Cleaning up test data...")
                
                # Clean up test data
                session.delete(generated_dataset)
                if updated_job:
                    session.delete(updated_job)
                
                datagen_event = session.query(DatagenEvent).filter(DatagenEvent.id == test_datagen_event_id).first()
                if datagen_event:
                    session.delete(datagen_event)
                    
                project = session.query(Project).filter(Project.id == test_project_id).first()
                if project:
                    session.delete(project)
                    
                session.commit()
                print("✅ Test data cleaned up")
                
                return True
            else:
                print("❌ No GeneratedDataset found")
                print("💡 The _save_generated_dataset_to_db method may have failed")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 Pipeline Save Functionality Test")
    print("Testing the database save method directly")
    print()
    
    success = test_pipeline_save_functionality()
    
    print("\n" + "=" * 55)
    if success:
        print("🎉 Pipeline save functionality test PASSED!")
        print("✅ The _save_generated_dataset_to_db method works correctly")
        print("✅ GeneratedDataset table is populated properly")
        print("✅ Foreign key relationships are established correctly")
        print("✅ Dataset content is preserved accurately")
        print("✅ Job status is updated appropriately")
        print()
        print("💡 The issue with dataset generation is in the LLM API calls,")
        print("   not in the database save functionality.")
    else:
        print("❌ Pipeline save functionality test FAILED!")
        print("💡 There may be an issue with the save method implementation")
    
    return success

if __name__ == "__main__":
    main()
