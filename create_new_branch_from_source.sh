
#!/bin/bash

# Check if branch names are provided
if [ $# -ne 2 ]; then
  echo "Usage: $0 <source-branch> <new-branch>"
  exit 1
fi

SOURCE_BRANCH=$1
NEW_BRANCH=$2

# Ensure we're in a Git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
  echo "Error: Not inside a Git repository"
  exit 1
fi

# Check if source branch exists
if ! git rev-parse --verify "$SOURCE_BRANCH" > /dev/null 2>&1; then
  echo "Error: Source branch '$SOURCE_BRANCH' does not exist"
  exit 1
fi

# Ensure the source branch is up-to-date with remote
echo "Pulling latest changes for '$SOURCE_BRANCH' from remote..."
git checkout "$SOURCE_BRANCH"
git pull origin "$SOURCE_BRANCH"

# Create and checkout new branch from source branch
echo "Creating and checking out new branch '$NEW_BRANCH' from '$SOURCE_BRANCH'..."
git checkout -b "$NEW_BRANCH"

# Push the new branch to remote
echo "Pushing new branch '$NEW_BRANCH' to remote..."
git push origin "$NEW_BRANCH"

echo "Preparation complete! Branch '$NEW_BRANCH' created and checked out locally."
