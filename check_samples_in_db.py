#!/usr/bin/env python3
"""
Check if there are any individual samples in the database.
"""

def check_samples_in_database():
    """Check what's currently in the database."""
    
    print("🔍 Checking Database for Individual Samples")
    print("=" * 50)
    
    try:
        from rainbowplus.api.database import get_db_session
        from rainbowplus.api.models import DatasetSample, GeneratedDataset, DatagenEvent, RainbowAnalysisJob
        
        with get_db_session() as session:
            # Check DatasetSample table
            samples = session.query(DatasetSample).all()
            print(f"📊 Found {len(samples)} individual samples in DatasetSample table")
            
            if samples:
                for i, sample in enumerate(samples[:5]):  # Show first 5
                    print(f"   Sample {i+1}:")
                    print(f"     ID: {sample.id}")
                    print(f"     Type: {sample.sample_type}")
                    print(f"     Label: {sample.label}")
                    print(f"     Content preview: {sample.content[:100]}...")
                    print(f"     Project ID: {sample.project_id}")
                    print(f"     Job ID: {sample.job_id}")
                    print(f"     Generated Dataset ID: {sample.generated_dataset_id}")
                    print()
            
            # Check GeneratedDataset table
            generated_datasets = session.query(GeneratedDataset).all()
            print(f"📊 Found {len(generated_datasets)} records in GeneratedDataset table")
            
            if generated_datasets:
                for i, dataset in enumerate(generated_datasets[:3]):  # Show first 3
                    print(f"   Dataset {i+1}:")
                    print(f"     ID: {dataset.id}")
                    print(f"     Status: {dataset.generation_status}")
                    print(f"     Total samples: {dataset.total_samples}")
                    print(f"     Project ID: {dataset.project_id}")
                    print(f"     Job ID: {dataset.job_id}")
                    print(f"     Created: {dataset.created_at}")
                    print()
            
            # Check recent jobs
            jobs = session.query(RainbowAnalysisJob).order_by(RainbowAnalysisJob.created_at.desc()).limit(5).all()
            print(f"📊 Found {len(jobs)} recent jobs in RainbowAnalysisJob table")
            
            if jobs:
                for i, job in enumerate(jobs):
                    print(f"   Job {i+1}:")
                    print(f"     ID: {job.id}")
                    print(f"     Status: {job.status}")
                    print(f"     Target LLM: {job.target_llm}")
                    print(f"     Project ID: {job.project_id}")
                    print(f"     Created: {job.created_at}")
                    print(f"     Has results: {job.results is not None}")
                    if job.results:
                        print(f"     Results type: {type(job.results)}")
                        if isinstance(job.results, dict):
                            print(f"     Results keys: {list(job.results.keys())}")
                    print()
            
            # Check datagen events
            events = session.query(DatagenEvent).order_by(DatagenEvent.created_at.desc()).limit(3).all()
            print(f"📊 Found {len(events)} recent datagen events")
            
            if events:
                for i, event in enumerate(events):
                    print(f"   Event {i+1}:")
                    print(f"     ID: {event.id}")
                    print(f"     Domain: {event.domain}")
                    print(f"     Dataset size: {event.dataset_size}")
                    print(f"     Project ID: {event.project_id}")
                    print(f"     Created: {event.created_at}")
                    print()
                    
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_samples_in_database()
