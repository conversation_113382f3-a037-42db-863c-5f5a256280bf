# Azure Deployment Guide for RainbowPlus

This guide covers deploying RainbowPlus to Azure with PostgreSQL and SSL support.

## Prerequisites

- Azure CLI installed and logged in
- Azure subscription with appropriate permissions
- Docker (for local testing)

## 1. Azure PostgreSQL Setup

### Create Azure PostgreSQL Server

```bash
# Set variables
RESOURCE_GROUP="rainbowplus-rg"
LOCATION="East US"
SERVER_NAME="rainbowplus-db-server"
ADMIN_USER="rainbowadmin"
ADMIN_PASSWORD="YourSecurePassword123!"

# Create resource group
az group create --name $RESOURCE_GROUP --location "$LOCATION"

# Create PostgreSQL server
az postgres server create \
  --resource-group $RESOURCE_GROUP \
  --name $SERVER_NAME \
  --location "$LOCATION" \
  --admin-user $ADMIN_USER \
  --admin-password $ADMIN_PASSWORD \
  --sku-name GP_Gen5_2 \
  --version 11 \
  --ssl-enforcement Enabled

# Create database
az postgres db create \
  --resource-group $RESOURCE_GROUP \
  --server-name $SERVER_NAME \
  --name rainbowplus
```

### Configure Firewall

```bash
# Allow Azure services
az postgres server firewall-rule create \
  --resource-group $RESOURCE_GROUP \
  --server $SERVER_NAME \
  --name "AllowAzureServices" \
  --start-ip-address 0.0.0.0 \
  --end-ip-address 0.0.0.0

# Allow your IP (replace with your actual IP)
az postgres server firewall-rule create \
  --resource-group $RESOURCE_GROUP \
  --server $SERVER_NAME \
  --name "AllowMyIP" \
  --start-ip-address YOUR_IP_ADDRESS \
  --end-ip-address YOUR_IP_ADDRESS
```

## 2. Environment Configuration

### Update .env.azure

```bash
# Copy the template and update with your values
cp .env.azure .env.production

# Edit .env.production with your Azure details:
POSTGRES_HOST=rainbowplus-db-server.postgres.database.azure.com
POSTGRES_USER=rainbowadmin@rainbowplus-db-server
POSTGRES_PASSWORD=YourSecurePassword123!
POSTGRES_DB=rainbowplus
POSTGRES_SSL=true
```

## 3. Database Setup

### Test Connection

```bash
# Load Azure environment and test connection
python setup_database.py --env-file .env.production --test-only
```

### Initialize Database

```bash
# Create tables and setup schema
python setup_database.py --env-file .env.production
```

## 4. Azure Container Apps Deployment

### Create Container Registry

```bash
# Create Azure Container Registry
ACR_NAME="rainbowplusacr"
az acr create \
  --resource-group $RESOURCE_GROUP \
  --name $ACR_NAME \
  --sku Basic \
  --admin-enabled true
```

### Build and Push Container

```bash
# Login to ACR
az acr login --name $ACR_NAME

# Build and push image
docker build -t $ACR_NAME.azurecr.io/rainbowplus:latest .
docker push $ACR_NAME.azurecr.io/rainbowplus:latest
```

### Deploy Container App

```bash
# Create Container Apps environment
az containerapp env create \
  --name rainbowplus-env \
  --resource-group $RESOURCE_GROUP \
  --location "$LOCATION"

# Deploy the app
az containerapp create \
  --name rainbowplus-api \
  --resource-group $RESOURCE_GROUP \
  --environment rainbowplus-env \
  --image $ACR_NAME.azurecr.io/rainbowplus:latest \
  --target-port 8000 \
  --ingress external \
  --env-vars \
    POSTGRES_HOST=rainbowplus-db-server.postgres.database.azure.com \
    POSTGRES_USER=rainbowadmin@rainbowplus-db-server \
    POSTGRES_PASSWORD=YourSecurePassword123! \
    POSTGRES_DB=rainbowplus \
    POSTGRES_SSL=true
```

## 5. SSL Configuration Details

### Why SSL is Required

Azure PostgreSQL requires SSL connections by default for security. The database configuration automatically detects Azure and enables SSL.

### SSL Connection String

The system automatically adds `?sslmode=require` to the connection string when connecting to Azure PostgreSQL.

### Local vs Azure Detection

```python
# Auto-detection logic in database.py
if 'azure' in host.lower() or 'postgres.database.azure.com' in host:
    use_ssl = True  # Azure requires SSL
else:
    use_ssl = False  # Local development
```

## 6. Environment Variables

### Required for Azure

```bash
POSTGRES_HOST=your-server.postgres.database.azure.com
POSTGRES_USER=admin@your-server
POSTGRES_PASSWORD=your_password
POSTGRES_DB=rainbowplus
POSTGRES_SSL=true  # or 'auto' for auto-detection
```

### Optional Performance Settings

```bash
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
```

## 7. Testing

### Local Testing with Azure Database

```bash
# Use Azure database from local development
python setup_database.py --env-file .env.production --test-only

# Run API locally with Azure database
export $(cat .env.production | xargs)
uvicorn rainbowplus.api.app:app --host 0.0.0.0 --port 8000
```

### Production Testing

```bash
# Test the deployed API
curl -X POST "https://your-app.azurecontainerapps.io/rainbowplus" \
     -H "Content-Type: application/json" \
     -d '{
       "prompts": ["Test prompt"],
       "target_llm": "gpt-3.5-turbo",
       "nickname": "test_user"
     }'
```

## 8. Monitoring and Logs

### View Container Logs

```bash
az containerapp logs show \
  --name rainbowplus-api \
  --resource-group $RESOURCE_GROUP \
  --follow
```

### Database Monitoring

```bash
# Check database metrics
az postgres server show \
  --resource-group $RESOURCE_GROUP \
  --name $SERVER_NAME
```

## 9. Troubleshooting

### Common SSL Issues

1. **Connection refused**: Check firewall rules
2. **SSL required**: Ensure `POSTGRES_SSL=true`
3. **Authentication failed**: Verify username format (`user@server`)

### Debug Connection

```bash
# Test direct connection with psql
psql "host=your-server.postgres.database.azure.com port=5432 dbname=rainbowplus user=admin@your-server password=your_password sslmode=require"
```

### Environment Switching

```bash
# Switch between environments easily
python setup_database.py --env local    # Local Docker
python setup_database.py --env azure    # Azure production
```

## 10. Security Best Practices

- Use Azure Key Vault for sensitive credentials
- Enable Azure AD authentication for PostgreSQL
- Use managed identities for container apps
- Regularly rotate passwords
- Monitor access logs

This setup provides a seamless experience for both local development with Docker and production deployment on Azure with SSL-enabled PostgreSQL.
