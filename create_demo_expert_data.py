#!/usr/bin/env python3
"""
Create demo data for expert review functionality with different expert assignments.
"""

import requests
import json

# API endpoints
BASE_URL = "http://localhost:8000"

def create_demo_data():
    """Create demo datagen events with different expert assignments."""
    
    print("🧪 Creating Demo Data for Expert Review")
    print("=" * 50)
    
    # Demo events with different expert assignments
    demo_events = [
        {
            "application_description": "Healthcare diagnostic AI system for analyzing patient symptoms and providing preliminary medical assessments",
            "domain": "healthcare",
            "example_input": "Patient reports chest pain, shortness of breath, and fatigue for the past week",
            "complexity": 8,
            "coverage": 90,
            "dataset_size": 500,
            "selected_experts": ["expert_003", "safety_expert"],
            "user_session_id": "demo-healthcare-001"
        },
        {
            "application_description": "Legal document analysis system for contract review and compliance checking",
            "domain": "legal",
            "example_input": "Review this employment contract for potential legal issues and compliance violations",
            "complexity": 7,
            "coverage": 85,
            "dataset_size": 300,
            "selected_experts": ["legal_expert", "expert_003"],
            "user_session_id": "demo-legal-001"
        },
        {
            "application_description": "Financial fraud detection system for identifying suspicious transactions",
            "domain": "finance",
            "example_input": "Analyze this transaction pattern: $500 withdrawal at 2 AM, followed by $1000 transfer to unknown account",
            "complexity": 9,
            "coverage": 95,
            "dataset_size": 800,
            "selected_experts": ["safety_expert", "test_expert"],
            "user_session_id": "demo-finance-001"
        },
        {
            "application_description": "Educational content moderation system for online learning platforms",
            "domain": "education",
            "example_input": "Review this student submission for inappropriate content or plagiarism",
            "complexity": 5,
            "coverage": 70,
            "dataset_size": 200,
            "selected_experts": ["test_expert"],
            "user_session_id": "demo-education-001"
        },
        {
            "application_description": "Customer service chatbot for handling product complaints and returns",
            "domain": "customer-service",
            "example_input": "I received a damaged product and want to return it for a full refund",
            "complexity": 4,
            "coverage": 75,
            "dataset_size": 400,
            "selected_experts": ["expert_003"],
            "user_session_id": "demo-customer-001"
        },
        {
            "application_description": "Security incident response system for analyzing potential cyber threats",
            "domain": "security",
            "example_input": "Detected unusual network activity: multiple failed login attempts from foreign IP addresses",
            "complexity": 10,
            "coverage": 98,
            "dataset_size": 1000,
            "selected_experts": ["safety_expert", "legal_expert", "test_expert"],
            "user_session_id": "demo-security-001"
        }
    ]
    
    created_events = []
    
    for i, event_data in enumerate(demo_events, 1):
        print(f"📝 Creating demo event {i}: {event_data['domain']} domain...")
        try:
            response = requests.post(f"{BASE_URL}/datagen/events", json=event_data)
            
            if response.status_code == 200:
                result = response.json()
                created_events.append(result)
                experts_str = ", ".join(event_data['selected_experts'])
                print(f"   ✅ Created event for experts: {experts_str}")
            else:
                print(f"   ❌ Failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎉 Created {len(created_events)} demo events!")
    return created_events

def test_expert_assignments():
    """Test that each expert can see their assigned events."""
    
    print("\n🔍 Testing Expert Assignments")
    print("=" * 40)
    
    experts = ["expert_003", "safety_expert", "legal_expert", "test_expert"]
    
    for expert_id in experts:
        print(f"\n👤 Testing expert: {expert_id}")
        try:
            response = requests.get(f"{BASE_URL}/datagen/events?expert_id={expert_id}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Found {data['total_count']} events assigned to {expert_id}")
                
                if data['events']:
                    for event in data['events'][:3]:  # Show first 3 events
                        domain = event.get('domain', 'Unknown')
                        desc = event.get('application_description', '')[:50]
                        print(f"      - {domain}: {desc}...")
                else:
                    print(f"      (No events assigned to {expert_id})")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def show_summary():
    """Show a summary of all events and expert assignments."""
    
    print("\n📊 Expert Assignment Summary")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/datagen/events?limit=100")
        
        if response.status_code == 200:
            data = response.json()
            events = data['events']
            
            # Count assignments per expert
            expert_counts = {}
            domain_counts = {}
            
            for event in events:
                experts = event.get('selected_experts', [])
                domain = event.get('domain', 'Unknown')
                
                # Count domain distribution
                domain_counts[domain] = domain_counts.get(domain, 0) + 1
                
                # Count expert assignments
                if experts:
                    for expert in experts:
                        expert_counts[expert] = expert_counts.get(expert, 0) + 1
            
            print(f"📈 Total Events: {len(events)}")
            print(f"📈 Total Domains: {len(domain_counts)}")
            
            print("\n👥 Expert Assignments:")
            for expert, count in sorted(expert_counts.items()):
                print(f"   {expert}: {count} events")
            
            print("\n🏷️ Domain Distribution:")
            for domain, count in sorted(domain_counts.items()):
                print(f"   {domain}: {count} events")
                
        else:
            print(f"❌ Failed to get summary: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting summary: {e}")

if __name__ == "__main__":
    created = create_demo_data()
    test_expert_assignments()
    show_summary()
    
    print("\n🌐 Demo Instructions:")
    print("1. Open http://localhost:3002 in your browser")
    print("2. Navigate to 'Expert Review' in the sidebar")
    print("3. Use the 'Demo: Switch Expert' dropdown to see different expert views")
    print("4. Each expert will see only their assigned events and related samples")
    print("5. Try filtering by domain to see how different experts work on different areas")
