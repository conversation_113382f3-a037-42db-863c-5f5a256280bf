"""
Health check endpoints for RainbowPlus API.
Provides comprehensive health monitoring for Azure and other cloud platforms.
"""

import os
import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import HTTPException
from sqlalchemy import text
import redis
import psutil

logger = logging.getLogger(__name__)


class HealthChecker:
    """Comprehensive health checker for RainbowPlus services."""
    
    def __init__(self):
        self.start_time = datetime.utcnow()
    
    async def check_database(self) -> Dict[str, Any]:
        """Check PostgreSQL database health."""
        try:
            from .database import engine
            
            start_time = time.time()
            
            with engine.connect() as conn:
                # Test basic connectivity
                result = conn.execute(text("SELECT 1 as health_check"))
                health_result = result.fetchone()
                
                # Test database version and info
                version_result = conn.execute(text("SELECT version()"))
                version = version_result.fetchone()[0]
                
                # Check connection pool status
                pool = engine.pool
                pool_status = {
                    "size": pool.size(),
                    "checked_in": pool.checkedin(),
                    "checked_out": pool.checkedout(),
                    "overflow": pool.overflow(),
                    "invalid": pool.invalid()
                }
                
                response_time = time.time() - start_time
                
                return {
                    "status": "healthy",
                    "response_time_ms": round(response_time * 1000, 2),
                    "version": version.split(',')[0],  # First part of version string
                    "pool_status": pool_status,
                    "ssl_enabled": "sslmode=require" in str(engine.url),
                    "host": str(engine.url.host),
                    "database": str(engine.url.database)
                }
                
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time_ms": None
            }
    
    async def check_redis(self) -> Dict[str, Any]:
        """Check Redis health for Celery."""
        try:
            redis_host = os.environ.get('REDIS_HOST', 'localhost')
            redis_port = int(os.environ.get('REDIS_PORT', '6379'))
            redis_password = os.environ.get('REDIS_PASSWORD')
            
            start_time = time.time()
            
            # Create Redis connection
            redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password,
                socket_timeout=5,
                socket_connect_timeout=5,
                decode_responses=True
            )
            
            # Test basic connectivity
            ping_result = redis_client.ping()
            
            # Get Redis info
            info = redis_client.info()
            
            response_time = time.time() - start_time
            
            return {
                "status": "healthy" if ping_result else "unhealthy",
                "response_time_ms": round(response_time * 1000, 2),
                "version": info.get('redis_version'),
                "connected_clients": info.get('connected_clients'),
                "used_memory_human": info.get('used_memory_human'),
                "host": redis_host,
                "port": redis_port
            }
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time_ms": None
            }
    
    async def check_celery(self) -> Dict[str, Any]:
        """Check Celery worker health."""
        try:
            from .tasks import celery_app
            
            start_time = time.time()
            
            # Get active workers
            inspect = celery_app.control.inspect()
            
            # Check active workers (with timeout)
            active_workers = inspect.active()
            registered_tasks = inspect.registered()
            stats = inspect.stats()
            
            response_time = time.time() - start_time
            
            if active_workers is None:
                return {
                    "status": "unhealthy",
                    "error": "No Celery workers responding",
                    "response_time_ms": round(response_time * 1000, 2)
                }
            
            worker_count = len(active_workers)
            total_active_tasks = sum(len(tasks) for tasks in active_workers.values())
            
            return {
                "status": "healthy" if worker_count > 0 else "degraded",
                "response_time_ms": round(response_time * 1000, 2),
                "worker_count": worker_count,
                "active_tasks": total_active_tasks,
                "workers": list(active_workers.keys()),
                "registered_tasks": len(registered_tasks.get(list(active_workers.keys())[0], [])) if active_workers else 0
            }
            
        except Exception as e:
            logger.error(f"Celery health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time_ms": None
            }
    
    async def check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Load average (Unix systems)
            try:
                load_avg = os.getloadavg()
            except (OSError, AttributeError):
                load_avg = None
            
            return {
                "status": "healthy",
                "cpu_percent": cpu_percent,
                "memory": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "percent_used": memory.percent
                },
                "disk": {
                    "total_gb": round(disk.total / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "percent_used": round((disk.used / disk.total) * 100, 2)
                },
                "load_average": load_avg
            }
            
        except Exception as e:
            logger.error(f"System resource check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def get_application_info(self) -> Dict[str, Any]:
        """Get application information."""
        uptime = datetime.utcnow() - self.start_time
        
        return {
            "name": "RainbowPlus API",
            "version": "1.0.0",
            "environment": os.environ.get('ENVIRONMENT', 'development'),
            "start_time": self.start_time.isoformat(),
            "uptime_seconds": int(uptime.total_seconds()),
            "uptime_human": str(uptime),
            "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
            "process_id": os.getpid()
        }


# Global health checker instance
health_checker = HealthChecker()
