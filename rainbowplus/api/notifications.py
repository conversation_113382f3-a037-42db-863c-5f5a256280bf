"""
Slack notification service for RainbowPlus API.
"""
import os
import logging
from typing import Optional, Dict, Any, List
import requests
from datetime import datetime
import statistics

logger = logging.getLogger(__name__)


class SlackNotificationService:
    """Service for sending Slack notifications when analysis jobs complete."""
    
    def __init__(self):
        self.webhook_url = os.environ.get('SLACK_WEBHOOK_URL')
        self.enabled = bool(self.webhook_url)
        
        if not self.enabled:
            logger.warning("Slack notifications disabled: SLACK_WEBHOOK_URL not configured")
    
    def send_completion_notification(
        self,
        nickname: str,
        job_id: str,
        status: str,
        prompts: list,
        target_llm: str,        
        target_llm_base_url: str,
        num_samples: int,
        num_mutations: int,
        max_iters: int,
        error_message: Optional[str] = None,
        score_results: Optional[Dict[str, List[float]]] = None
    ) -> bool:
        """
        Send a Slack notification when an analysis job completes.

        Args:
            nickname: User's nickname for the notification
            job_id: Unique identifier for the job
            status: Job status ('completed', 'failed', etc.)
            prompts: List of prompts that were analyzed
            target_llm: Target LLM used for analysis
            num_samples: Number of samples used
            num_mutations: Number of mutations used
            max_iters: Maximum iterations used
            error_message: Error message if job failed
            score_results: Dictionary of score results from analysis

        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        if not self.enabled:
            logger.debug("Slack notifications disabled, skipping notification")
            return False
            
        try:
            message = self._build_message(
                nickname, job_id, status, prompts, target_llm,
                num_samples, num_mutations, max_iters, error_message, score_results,target_llm_base_url
            )
            
            response = requests.post(
                self.webhook_url,
                json=message,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Slack notification sent successfully for job {job_id}")
                return True
            else:
                logger.error(f"Failed to send Slack notification: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending Slack notification: {str(e)}")
            return False
    
    def _build_message(
        self,
        nickname: str,
        job_id: str,
        status: str,
        prompts: list,
        target_llm: str,
        num_samples: int,
        num_mutations: int,
        max_iters: int,
        error_message: Optional[str] = None,
        score_results: Optional[Dict[str, List[float]]] = None,
        target_llm_base_url: str = None
    ) -> Dict[str, Any]:
        """Build the Slack message payload."""
        
        # Determine color and emoji based on status
        if status == 'completed':
            color = "good"
            emoji = "✅"
            title = "RainbowPlus Analysis Completed"
        elif status == 'failed':
            color = "danger"
            emoji = "❌"
            title = "RainbowPlus Analysis Failed"
        else:
            color = "warning"
            emoji = "⚠️"
            title = f"RainbowPlus Analysis Status: {status}"
        
        # Build the main message
        text = f"{emoji} Hey {nickname}! Your RainbowPlus analysis is {status}."
        
        # Build attachment with job details
        fields = [
            {
                "title": "Job ID",
                "value": f"`{job_id}`",
                "short": True
            },
            {
                "title": "Target LLM",
                "value": target_llm,
                "short": True
            },
            {
                "title": "Target LLM openai api",
                "value": target_llm_base_url,
                "short": True
            },
            {
                "title": "Samples",
                "value": str(num_samples),
                "short": True
            },
            {
                "title": "Mutations",
                "value": str(num_mutations),
                "short": True
            },
            {
                "title": "Max Iterations",
                "value": str(max_iters),
                "short": True
            },
            {
                "title": "Prompts Count",
                "value": str(len(prompts)),
                "short": True
            }
        ]
        
        # Add score statistics if available and job completed successfully
        if score_results and status == 'completed':
            score_stats = self._calculate_score_statistics(score_results)
            if score_stats:
                fields.append({
                    "title": "Score Results",
                    "value": score_stats,
                    "short": False
                })

        # Add error message if job failed
        if error_message and status == 'failed':
            fields.append({
                "title": "Error",
                "value": f"```{error_message[:500]}{'...' if len(error_message) > 500 else ''}```",
                "short": False
            })
        
        # Add sample prompts (first 2)
        if prompts:
            sample_prompts = prompts[:2]
            prompts_text = "\n".join([f"• {prompt[:100]}{'...' if len(prompt) > 100 else ''}" for prompt in sample_prompts])
            if len(prompts) > 2:
                prompts_text += f"\n... and {len(prompts) - 2} more"
            
            fields.append({
                "title": "Sample Prompts",
                "value": prompts_text,
                "short": False
            })
        
        attachment = {
            "color": color,
            "title": title,
            "fields": fields,
            "footer": "RainbowPlus API",
            "ts": int(datetime.now().timestamp())
        }
        
        return {
            "text": text,
            "attachments": [attachment]
        }

    def _calculate_score_statistics(self, score_results: Dict[str, List[float]]) -> str:
        """Calculate and format score statistics for display."""
        try:
            if not score_results:
                return "No score data available"

            stats_lines = []
            all_scores = []

            # Collect statistics for each descriptor
            for descriptor, scores in score_results.items():
                if scores and isinstance(scores, list):  # Only process if there are scores
                    all_scores.extend(scores)
                    avg_score = statistics.mean(scores)
                    max_score = max(scores)
                    min_score = min(scores)

                    # Format descriptor name (clean up tuple string format)
                    clean_descriptor = str(descriptor).strip("()\"'").replace("'", "").replace('"', '')

                    stats_lines.append(
                        f"**{clean_descriptor}**: "
                        f"Avg: {avg_score:.4f}, Max: {max_score:.4f}, Min: {min_score:.4f} "
                        f"({len(scores)} samples)"
                    )

            # Add overall statistics if we have scores from multiple descriptors
            if len(score_results) > 1 and all_scores:
                overall_avg = statistics.mean(all_scores)
                overall_max = max(all_scores)
                overall_min = min(all_scores)

                stats_lines.insert(0,
                    f"**Overall**: "
                    f"Avg: {overall_avg:.4f}, Max: {overall_max:.4f}, Min: {overall_min:.4f} "
                    f"({len(all_scores)} total samples)"
                )
                stats_lines.insert(1, "")  # Add blank line for readability

            return "\n".join(stats_lines) if stats_lines else "No score data available"

        except Exception as e:
            logger.error(f"Error calculating score statistics: {str(e)}")
            return f"Error processing scores: {str(e)}"


# Global instance
slack_service = SlackNotificationService()
