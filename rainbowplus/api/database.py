from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager
from typing import Generator
import os
import logging

logger = logging.getLogger(__name__)


def get_database_url() -> str:
    """
    Build database URL with appropriate SSL settings for local vs Azure.

    Returns:
        str: Database connection URL
    """
    # Required environment variables
    user = os.environ.get('POSTGRES_USER', 'myuser')
    password = os.environ.get('POSTGRES_PASSWORD', 'mypassword')
    database = os.environ.get('POSTGRES_DB', 'mydb')
    host = os.environ.get('POSTGRES_HOST', 'localhost')
    port = os.environ.get('POSTGRES_PORT', '5432')
    # Print environment values
    logger.info(f"Database connection parameters:")
    logger.info(f"  User: {user}")
    logger.info(f"  Database: {database}")
    logger.info(f"  pw: {password}")
    logger.info(f"  Host: {host}")
    logger.info(f"  Port: {port}")    
    
    # URL encode password to handle special characters
    from urllib.parse import quote_plus
    encoded_password = quote_plus(password)

    # Base URL with encoded password
    base_url = f'postgresql://{user}:{encoded_password}@{host}:{port}/{database}'

    # Determine if we need SSL (Azure or production)
    use_ssl = os.environ.get('POSTGRES_SSL', 'auto').lower()

    if use_ssl == 'auto':
        # Auto-detect: use SSL for Azure, no SSL for localhost
        use_ssl = 'true' if 'azure' in host.lower() or 'postgres.database.azure.com' in host else 'false'

    if use_ssl == 'true':
        # Azure PostgreSQL requires SSL with specific parameters
        ssl_params = '?sslmode=require&sslcert=&sslkey=&sslrootcert='
        logger.info(f"🔒 Using SSL connection for database: {host}")
        return base_url + ssl_params
    else:
        # Local development without SSL
        logger.info(f"🔓 Using non-SSL connection for database: {host}")
        return base_url


def create_database_engine():
    """Create database engine with appropriate configuration."""
    database_url = get_database_url()

    # Engine configuration
    engine_kwargs = {
        'echo': os.environ.get('DB_ECHO', 'false').lower() == 'true',  # SQL logging
        'pool_size': int(os.environ.get('DB_POOL_SIZE', '5')),
        'max_overflow': int(os.environ.get('DB_MAX_OVERFLOW', '10')),
        'pool_timeout': int(os.environ.get('DB_POOL_TIMEOUT', '30')),
        'pool_recycle': int(os.environ.get('DB_POOL_RECYCLE', '3600')),  # 1 hour
    }

    logger.info(f"📊 Creating database engine with pool_size={engine_kwargs['pool_size']}")

    return create_engine(database_url, **engine_kwargs)


# Create engine
engine = create_database_engine()

# Create session factory
SessionLocal = sessionmaker(bind=engine, autocommit=False, autoflush=False)

# Base class for declarative models
Base = declarative_base()

@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    Provide a transactional scope around a series of operations.
    
    Usage:
        with get_db_session() as session:
            # Perform database operations
            session.add(some_object)
    """
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()