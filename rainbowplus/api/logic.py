from typing import Dict, List, Tuple, Optional, Any
from .models import PointResponse

def _process_results_logic(
    adv_prompts: Dict[str, List[str]],
    responses: Dict[str, List[str]],
    scores: Dict[str, List[float]],
) -> List[PointResponse]:
    """
    Process evaluation results into a list of PointResponse objects. Same logic as _process_results in RainbowPlusService. Splited due to fix circular import

    :param adv_prompts: adversarial prompts with descriptors as keys
    :param responses: model responses with descriptors as keys
    :param scores: safety scores with descriptors as keys
    :return: a list of PointResponse objects
    """
    try:
        points = []
        keys = list(adv_prompts.keys())
        for key in keys:
            length = len(adv_prompts.get(key))
            for idx in range(length):
                point = PointResponse(
                    adv_prompt=adv_prompts.get(key)[idx],
                    response=responses.get(key)[idx],
                    score=scores.get(key)[idx],
                    descriptor=str(key),
                )
                points.append(point)
        return points
    except Exception as e:
        print(e)
        return []
