from dataclasses import dataclass, field
from typing import Dict, Any


@dataclass
class LLMConfig:
    """Configuration for a Language Model."""

    type_: str
    api_key: str = None
    base_url: str = None
    azure_endpoint: str = None  # For Azure OpenAI
    api_version: str = None     # For Azure OpenAI
    model_kwargs: Dict[str, Any] = field(default_factory=dict)
    sampling_params: Dict[str, Any] = field(default_factory=dict)
