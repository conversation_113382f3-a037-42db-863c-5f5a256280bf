#!/usr/bin/env python3
"""
Database migration script for configuration tables.
Creates the necessary tables for storing configuration templates.

Usage:
    python -m rainbowplus.configs.migrate_database
    python -m rainbowplus.configs.migrate_database --env local
    python -m rainbowplus.configs.migrate_database --env azure
"""

import os
import sys
import argparse
from pathlib import Path
import logging

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_environment(env: str = None, env_file: str = None):
    """Load environment variables from file."""
    from dotenv import load_dotenv
    
    if env_file:
        env_path = Path(env_file)
    elif env:
        env_path = project_root / f".env.{env}"
    else:
        env_path = project_root / ".env"
    
    if env_path.exists():
        logger.info(f"📁 Loading environment from: {env_path}")
        load_dotenv(env_path)
        return True
    else:
        logger.warning(f"⚠️  Environment file not found: {env_path}")
        return False


def create_configuration_tables():
    """Create configuration tables in the database."""
    try:
        from rainbowplus.api.database import engine
        from rainbowplus.configs.database_models import Base
        
        logger.info("🔧 Creating configuration tables...")
        
        # Create tables
        Base.metadata.create_all(engine)
        
        logger.info("✅ Configuration tables created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create configuration tables: {e}")
        return False


def test_database_connection():
    """Test database connection."""
    try:
        from rainbowplus.api.database import engine
        from sqlalchemy import text
        
        logger.info("🔗 Testing database connection...")
        
        # Print engine details for debugging
        logger.info(f"Engine: {engine}")
        logger.info(f"SQLAlchemy version: {engine.dialect.driver}")
        
        with engine.connect() as conn:
            # Use text() to create an executable SQL statement
            stmt = text("SELECT version()")
            result = conn.execute(stmt)
            version = result.scalar()
            logger.info(f"✅ Database connection successful!")
            logger.info(f"   PostgreSQL version: {version}")
            
            # Check if it's Azure PostgreSQL
            if 'azure' in str(version).lower():
                logger.info("☁️  Connected to Azure PostgreSQL")
            else:
                logger.info("🐳 Connected to local PostgreSQL")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error details: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def check_existing_tables():
    """Check if configuration tables already exist."""
    try:
        from rainbowplus.api.database import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            # Check for configuration_templates table
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'configuration_templates'
                );
            """))
            
            templates_exist = result.fetchone()[0]
            
            # Check for configuration_history table
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'configuration_history'
                );
            """))
            
            history_exist = result.fetchone()[0]
            
            if templates_exist and history_exist:
                logger.info("ℹ️  Configuration tables already exist")
                return True
            elif templates_exist or history_exist:
                logger.warning("⚠️  Some configuration tables exist, but not all")
                return False
            else:
                logger.info("ℹ️  Configuration tables do not exist yet")
                return False
                
    except Exception as e:
        logger.error(f"❌ Failed to check existing tables: {e}")
        return False


def import_yaml_config_to_database(yaml_file: str, template_name: str):
    """Import a YAML configuration file to the database as a template.
    
    Args:
        yaml_file: Path to YAML configuration file
        template_name: Name for the database template
    """
    try:
        from rainbowplus.configs.loaders import create_yaml_loader
        from rainbowplus.configs.database_service import ConfigurationDatabaseService
        
        logger.info(f"📥 Importing YAML config '{yaml_file}' as template '{template_name}'...")
        
        # Load configuration from YAML
        yaml_loader = create_yaml_loader()
        config = yaml_loader.load(yaml_file)
        
        # Save to database
        db_service = ConfigurationDatabaseService()
        template_id = db_service.create_template(
            name=template_name,
            config=config,
            description=f"Imported from {yaml_file}",
            created_by="migration_script"
        )
        
        logger.info(f"✅ Successfully imported config as template '{template_name}' (ID: {template_id})")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to import YAML config: {e}")
        return False


def main():
    """Main migration function."""
    parser = argparse.ArgumentParser(description="Migrate configuration tables")
    parser.add_argument("--env", choices=["local", "azure"], help="Environment preset")
    parser.add_argument("--env-file", help="Custom environment file path")
    parser.add_argument("--test-only", action="store_true", help="Only test connection")
    parser.add_argument("--import-yaml", help="Import YAML config file to database")
    parser.add_argument("--template-name", help="Template name for imported YAML config")
    
    args = parser.parse_args()
    
    print("🚀 RainbowPlus Configuration Database Migration")
    print("   Setting up configuration tables for database-based configs")
    print()
    
    # Load environment
    if not load_environment(args.env, args.env_file):
        logger.warning("⚠️  No environment file loaded, using system environment")
    
    # Test connection
    if not test_database_connection():
        logger.error("💡 Troubleshooting tips:")
        logger.error("   - Check if PostgreSQL is running")
        logger.error("   - Verify connection parameters in environment file")
        logger.error("   - For Azure: ensure firewall allows your IP")
        logger.error("   - For local: ensure Docker container is running")
        return False
    
    if args.test_only:
        logger.info("🎉 Connection test completed successfully!")
        return True
    
    # Check existing tables
    tables_exist = check_existing_tables()
    
    # Create tables if needed
    if not tables_exist:
        if not create_configuration_tables():
            return False
    
    # Import YAML config if requested
    if args.import_yaml:
        if not args.template_name:
            logger.error("❌ --template-name is required when using --import-yaml")
            return False
        
        if not import_yaml_config_to_database(args.import_yaml, args.template_name):
            return False
    
    logger.info("🎉 Configuration database migration completed successfully!")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
