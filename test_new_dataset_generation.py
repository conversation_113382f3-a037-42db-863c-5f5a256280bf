#!/usr/bin/env python3
"""
Test new dataset generation to ensure individual samples are created automatically.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_new_dataset_generation():
    """Test that new dataset generation creates individual samples."""
    
    print("🧪 Testing New Dataset Generation with Individual Samples")
    print("=" * 60)
    
    try:
        # Step 1: Create a datagen event
        print("📝 Step 1: Creating datagen event...")
        
        datagen_event_data = {
            "application_description": "Test application for individual sample verification",
            "domain": "Testing",
            "example_input": "Generate test incident reports",
            "complexity": 3,
            "coverage": 50,
            "dataset_size": 6,  # Small dataset for testing
            "user_session_id": "test-session-individual-samples"
        }
        
        response = requests.post(f"{BASE_URL}/datagen/events", json=datagen_event_data)
        
        if response.status_code == 200:
            event_response = response.json()
            print(f"✅ Datagen event created: {event_response['event_id']}")
        else:
            print(f"❌ Failed to create datagen event: {response.status_code} - {response.text}")
            return
        
        # Step 2: Generate dataset
        print("\n📝 Step 2: Generating dataset...")
        
        dataset_generation_data = {
            "description": "Generate realistic incident reports for testing individual sample storage",
            "examples": ["Report a theft incident", "Report a harassment case"],
            "task": "Content",
            "llm": {
                "provider": "openai",
                "api_key": "test-key",  # This will use the key from base.yml
                "model_kwargs": {
                    "model": "gpt-4o-mini"
                },
                "sampling_params": {
                    "n": 6,  # Generate 6 samples
                    "temperature": 0.7,
                    "max_tokens": 500
                }
            }
        }
        
        response = requests.post(f"{BASE_URL}/datasets/generate", json=dataset_generation_data)
        
        if response.status_code == 200:
            generation_response = response.json()
            job_id = generation_response['job_id']
            print(f"✅ Dataset generation started: {job_id}")
            
            # Wait for completion (check every 5 seconds for up to 2 minutes)
            print("⏳ Waiting for dataset generation to complete...")
            
            for i in range(24):  # 24 * 5 = 120 seconds = 2 minutes
                time.sleep(5)
                
                # Check if there are new individual samples
                response = requests.get(f"{BASE_URL}/datasets/samples?job_id={job_id}")
                if response.status_code == 200:
                    data = response.json()
                    if data['total_count'] > 0:
                        print(f"✅ Found {data['total_count']} individual samples for job {job_id}")
                        
                        # Show some sample details
                        for j, sample in enumerate(data['samples'][:3]):
                            print(f"   Sample {j+1}:")
                            print(f"     ID: {sample['id']}")
                            print(f"     Label: {sample['label']}")
                            print(f"     Content preview: {sample['content'][:100]}...")
                            print(f"     Sample group: {sample['sample_group']}")
                            print(f"     Sample index: {sample['sample_index']}")
                            print()
                        
                        print("🎉 Individual sample storage is working correctly!")
                        return
                
                print(f"   Attempt {i+1}/24: No samples found yet, waiting...")
            
            print("⏰ Timeout waiting for dataset generation to complete")
            
        else:
            print(f"❌ Failed to start dataset generation: {response.status_code} - {response.text}")
        
    except Exception as e:
        print(f"❌ Error testing new dataset generation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_dataset_generation()
