archive:
  path: [./configs/categories/categories.txt, ./configs/styles/styles.txt]
  descriptor: [Risk Category, Attack Style]

sample_prompts: ./data/do-not-answer.json

target_llm:
  type_: openai
  api_key: your-api-key-here
  base_url: https://api.deepinfra.com/v1/openai
  model_kwargs:
    model: Gryphe/MythoMax-L2-13b
    max_model_len: 4096

  sampling_params:
    temperature: 0.8
    top_p: 0.95
    max_tokens: 1024

mutator_llm:
  type_: openai
  api_key: your-api-key-here
  base_url: https://api.deepinfra.com/v1/openai

  model_kwargs:
    model: Gryphe/MythoMax-L2-13b
    max_model_len: 2048

  sampling_params:
    temperature: 0.7
    top_p: 0.9
    max_completion_tokens: 128

# Example: Using Azure OpenAI for judge_llm
judge_llm:
  type_: azure_openai
  api_key: your-azure-api-key-here
  azure_endpoint: https://your-resource.openai.azure.com/
  api_version: 2024-02-15-preview

  model_kwargs:
    model: gpt-4
    max_model_len: 4096

  sampling_params:
    temperature: 0.6
    top_p: 0.9
    max_completion_tokens: 1024

# Example: Using Azure OpenAI for fitness_llm
fitness_llm:
  type_: azure_openai
  api_key: your-azure-api-key-here
  azure_endpoint: https://your-resource.openai.azure.com/
  api_version: 2024-02-15-preview

  model_kwargs:
    model: gpt-4
    max_model_len: 4096

  sampling_params:
    temperature: 0.8
    top_p: 0.95
    max_completion_tokens: 1024
    logprobs: True
    top_logprobs: 1
