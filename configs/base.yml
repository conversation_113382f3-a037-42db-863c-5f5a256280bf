archive:
  path: [./configs/categories/categories.txt, ./configs/styles/styles.txt]
  descriptor: [Risk Category, Attack Style]

sample_prompts: ./data/do-not-answer.json

target_llm:
  type_: vllm
  model_kwargs:
    model: Qwen/Qwen2.5-3B-Instruct
    max_model_len: 4096
    trust_remote_code: True
    gpu_memory_utilization: 0.5

  sampling_params:
    temperature: 0.8
    top_p: 0.95
    max_tokens: 1024

mutator_llm:
  type_: openai # or vllm
  api_key: ********************************************************************************************************************************************************************

  model_kwargs:
    model: gpt-4-turbo
    max_model_len: 2048

  sampling_params:
    temperature: 0.7
    top_p: 0.9
    max_tokens: 128

judge_llm:
  type_: azure_openai # or vllm
  api_key: 6lF6W9fSrQjRaC5Eq9YygLU8LjuOrPLe2JpnHIvigPf7iHzcnrZJJQQJ99BGAC77bzfXJ3w3AAAAACOGCubz
  azure_endpoint: https://baldr-mcqem0x3-southindia.cognitiveservices.azure.com/
  api_version: 2025-01-01-preview

  model_kwargs:
    model: Llama-4-Scout-17B-16E-Instruct
    max_model_len: 4096

  sampling_params:
    temperature: 0.6
    top_p: 0.9
    max_completion_tokens: 1024

# fitness_llm:
#   type_: vllm

#   model_kwargs:
#     model: meta-llama/Llama-Guard-3-1B
#     trust_remote_code: True
#     gpu_memory_utilization: 0.15
#     max_model_len: 4096

#   sampling_params:
#     temperature: 0.7
#     top_p: 0.9
#     max_tokens: 16
#     logprobs: 1
# UNCOMMENT TO USE A OPENAI MODEL FOR FITNESS
fitness_llm:
  type_: openai # or vllm
  api_key: ********************************************************************************************************************************************************************

  model_kwargs:
    model: gpt-4-turbo
    max_model_len: 4096

  sampling_params:
    temperature: 0.8
    top_p: 0.95
    max_tokens: 1024
    logprobs: True
    top_logprobs: 1
