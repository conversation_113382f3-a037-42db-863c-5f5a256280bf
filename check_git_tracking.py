import subprocess
import sys
import os

def check_git_tracking(files):
    """
    Check if files are tracked in git repository
    Returns a dictionary with file paths and their tracking status
    """
    if not os.path.exists('.git'):
        print("Error: Not a git repository")
        return {}
    
    try:
        # Get list of tracked files
        result = subprocess.run(['git', 'ls-files'], capture_output=True, text=True, check=True)
        tracked_files = set(result.stdout.splitlines())
    except subprocess.CalledProcessError as e:
        print(f"Git command failed: {e.stderr}")
        return {}
    
    status = {}
    for file_path in files:
        abs_path = os.path.abspath(file_path)
        # Check if file exists
        if not os.path.exists(file_path):
            status[file_path] = 'File not found'
            continue
            
        # Check if in tracked files
        if file_path in tracked_files:
            status[file_path] = 'Tracked'
        else:
            status[file_path] = 'Not tracked'
    
    return status

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python check_git_tracking.py <file1> [file2] ...")
        sys.exit(1)
        
    files_to_check = sys.argv[1:]
    results = check_git_tracking(files_to_check)
    
    print("\nGit Tracking Status:")
    max_len = max(len(f) for f in results.keys()) if results else 0
    for file, status in results.items():
        print(f"{file.ljust(max_len)} : {status}")