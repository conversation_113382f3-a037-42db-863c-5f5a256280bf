# Health Checks for RainbowPlus API

This document covers the comprehensive health check system for RainbowPlus, including Azure Container Apps integration and Celery monitoring.

## Health Check Endpoints

### Basic Health Check
```
GET /health
```
**Purpose**: Simple health check for Azure and cloud platforms  
**Response Time**: < 100ms  
**Use Case**: Liveness probes, basic monitoring

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "service": "RainbowPlus API",
  "version": "1.0.0"
}
```

### Detailed Health Check
```
GET /health/detailed
```
**Purpose**: Comprehensive health check including all dependencies  
**Response Time**: < 2s  
**Use Case**: Readiness probes, detailed monitoring

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "checks": {
    "application": {
      "name": "RainbowPlus API",
      "version": "1.0.0",
      "uptime_seconds": 3600,
      "environment": "production"
    },
    "database": {
      "status": "healthy",
      "response_time_ms": 45.2,
      "version": "PostgreSQL 13.8",
      "ssl_enabled": true,
      "pool_status": {
        "size": 10,
        "checked_out": 2,
        "overflow": 0
      }
    },
    "redis": {
      "status": "healthy",
      "response_time_ms": 12.1,
      "version": "6.2.0",
      "connected_clients": 5
    },
    "celery": {
      "status": "healthy",
      "response_time_ms": 156.3,
      "worker_count": 2,
      "active_tasks": 1,
      "workers": ["worker1@hostname", "worker2@hostname"]
    },
    "system": {
      "status": "healthy",
      "cpu_percent": 25.4,
      "memory": {
        "total_gb": 8.0,
        "available_gb": 5.2,
        "percent_used": 35.0
      }
    }
  }
}
```

### Component-Specific Health Checks

#### Database Health
```
GET /health/database
```
Tests PostgreSQL connectivity, SSL status, and connection pool health.

#### Celery Health
```
GET /health/celery
```
Tests Celery worker availability, task processing, and queue status.

#### Redis Health
```
GET /health/redis
```
Tests Redis connectivity and basic operations.

## Azure Container Apps Integration

### Liveness Probe Configuration
```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 8000
    scheme: HTTP
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3
```

### Readiness Probe Configuration
```yaml
readinessProbe:
  httpGet:
    path: /health/detailed
    port: 8000
    scheme: HTTP
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 10
  successThreshold: 1
  failureThreshold: 3
```

### Azure CLI Deployment with Health Checks
```bash
az containerapp create \
  --name rainbowplus-api \
  --resource-group $RESOURCE_GROUP \
  --environment rainbowplus-env \
  --image $ACR_NAME.azurecr.io/rainbowplus:latest \
  --target-port 8000 \
  --ingress external \
  --probe-type liveness \
  --probe-http-get-path /health \
  --probe-initial-delay 30 \
  --probe-period 30 \
  --probe-timeout 5 \
  --probe-failure-threshold 3
```

## Celery Health Monitoring

### Standalone Celery Health Check
```bash
# Check all Celery components
python celery_health_monitor.py --full-report

# Check only workers
python celery_health_monitor.py --check-workers

# Check only queue
python celery_health_monitor.py --check-queue

# Test task submission
python celery_health_monitor.py --test-task

# Get metrics as JSON
python celery_health_monitor.py --metrics

# Exit with error code if unhealthy (for scripts)
python celery_health_monitor.py --exit-code
```

### Celery Worker Health Indicators

**Healthy Worker**:
- Responds to inspection commands
- Has registered tasks
- Can process test tasks
- Connection to Redis broker

**Unhealthy Worker**:
- No response to inspection
- High number of failed tasks
- Cannot connect to broker
- Memory/CPU exhaustion

### Queue Health Monitoring

**Queue Metrics**:
- Pending task count
- Processing rate
- Failed task count
- Worker availability

**Redis Broker Health**:
- Connection status
- Memory usage
- Client connections
- Persistence status

## Health Status Definitions

### Status Levels

**healthy**: All systems operational
- All dependencies responding
- Performance within normal ranges
- No critical errors

**degraded**: Some non-critical issues
- Non-critical services down (e.g., some Celery workers)
- Performance slightly degraded
- Service still functional

**unhealthy**: Critical issues
- Database unavailable
- No Celery workers
- Service cannot function

### HTTP Status Codes

- `200 OK`: Healthy or degraded
- `503 Service Unavailable`: Unhealthy

## Monitoring Integration

### Prometheus Metrics
Health check endpoints expose metrics compatible with Prometheus:

```
# HELP rainbowplus_health_status Health status of RainbowPlus service
# TYPE rainbowplus_health_status gauge
rainbowplus_health_status{component="database"} 1
rainbowplus_health_status{component="celery"} 1
rainbowplus_health_status{component="redis"} 1

# HELP rainbowplus_response_time_seconds Response time for health checks
# TYPE rainbowplus_response_time_seconds histogram
rainbowplus_response_time_seconds{endpoint="/health/database"} 0.045
```

### Azure Monitor Integration
```bash
# Create Azure Monitor alert for health check failures
az monitor metrics alert create \
  --name "RainbowPlus Health Check Failed" \
  --resource-group $RESOURCE_GROUP \
  --scopes "/subscriptions/$SUBSCRIPTION/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.App/containerApps/rainbowplus-api" \
  --condition "avg HttpResponseTime > 5000" \
  --description "RainbowPlus health check response time too high"
```

## Troubleshooting

### Common Issues

**Database Connection Failures**:
```bash
# Test database connectivity
python diagnose_azure_connection.py

# Check firewall rules
az postgres server firewall-rule list --resource-group $RG --server-name $SERVER
```

**Celery Worker Issues**:
```bash
# Check worker logs
celery -A rainbowplus.api.tasks worker --loglevel=info

# Inspect workers
celery -A rainbowplus.api.tasks inspect active

# Purge queue
celery -A rainbowplus.api.tasks purge
```

**Redis Connection Issues**:
```bash
# Test Redis connectivity
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping

# Check Redis info
redis-cli -h $REDIS_HOST -p $REDIS_PORT info
```

### Health Check Debugging

**Enable Debug Logging**:
```bash
export LOG_LEVEL=DEBUG
uvicorn rainbowplus.api.app:app --log-level debug
```

**Manual Health Check Testing**:
```bash
# Test all endpoints
curl http://localhost:8000/health
curl http://localhost:8000/health/detailed
curl http://localhost:8000/health/database
curl http://localhost:8000/health/celery
curl http://localhost:8000/health/redis
```

## Best Practices

1. **Use appropriate timeouts** for each health check type
2. **Monitor trends** not just current status
3. **Set up alerts** for degraded states before they become critical
4. **Test health checks** during deployment
5. **Document dependencies** and their health check requirements
6. **Use circuit breakers** for external dependencies
7. **Implement graceful degradation** when possible

This health check system provides comprehensive monitoring for RainbowPlus in Azure and other cloud environments, ensuring reliable service operation and quick issue detection.
