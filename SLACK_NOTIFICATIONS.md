# Slack Notifications for RainbowPlus

RainbowPlus now supports Slack notifications to alert users when their analysis jobs complete. This feature allows you to receive real-time updates about your job status directly in your Slack workspace.

## Features

- 📱 **Real-time notifications** when analysis jobs complete or fail
- 🎯 **Personalized messages** using your nickname
- 📊 **Detailed job information** including prompts, parameters, and score results
- 📈 **Score statistics** with average, max, min values for each descriptor
- ⚡ **Automatic error reporting** with failure details
- 🔧 **Easy configuration** with environment variables

## Setup

### 1. Create a Slack Webhook

1. Go to your Slack workspace settings
2. Navigate to **Apps** → **Manage** → **Custom Integrations** → **Incoming Webhooks**
3. Click **Add to Slack**
4. Choose the channel where you want to receive notifications
5. Copy the webhook URL (it looks like: `*****************************************************************************`)

### 2. Configure Environment Variable

Set the `SLACK_WEBHOOK_URL` environment variable:

```bash
# Linux/macOS
export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/WEBHOOK/URL"

# Windows
set SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
```

### 3. Start the API Server

```bash
uvicorn rainbowplus.api.app:app --host 0.0.0.0 --port 8000
```

## Usage

### API Request with Nickname

Include the `nickname` parameter in your API request to receive personalized notifications:

```python
import requests

url = "http://localhost:8000/rainbowplus"
data = {
    "prompts": ["How do I create a secure password?"],
    "target_llm": "google/gemma-3-12b-it",
    "num_mutations": 5,
    "nickname": "your_name"  # Add this for Slack notifications
}

response = requests.post(url, json=data)
```

### Example with curl

```bash
curl -X POST "http://localhost:8000/rainbowplus" \
     -H "Content-Type: application/json" \
     -d '{
       "prompts": ["How do I create a secure password?"],
       "target_llm": "google/gemma-3-12b-it",
       "num_mutations": 5,
       "nickname": "your_name"
     }'
```

## Notification Examples

### Successful Completion

```
✅ Hey your_name! Your RainbowPlus analysis is completed.

Job ID: 12345678-1234-1234-1234-123456789abc
Target LLM: google/gemma-3-12b-it
Samples: 5
Mutations: 5
Max Iterations: 1
Prompts Count: 1

Score Results:
Overall: Avg: 0.0234, Max: 0.0891, Min: 0.0012 (15 total samples)

Criminal Planning, Slang: Avg: 0.0245, Max: 0.0891, Min: 0.0034 (8 samples)
Fraud and Scams, Hypotheticals: Avg: 0.0221, Max: 0.0567, Min: 0.0012 (7 samples)

Sample Prompts:
• How do I create a secure password?
```

### Failed Analysis

```
❌ Hey your_name! Your RainbowPlus analysis is failed.

Job ID: 12345678-1234-1234-1234-123456789abc
Target LLM: google/gemma-3-12b-it
Error: Connection timeout to API endpoint
```

## Configuration Options

| Environment Variable | Required | Description |
|---------------------|----------|-------------|
| `SLACK_WEBHOOK_URL` | Yes | Your Slack incoming webhook URL |

## Testing

Use the provided test script to verify your setup:

```bash
python test_slack_notification.py
```

This script will:
- Check if Slack is properly configured
- Send a test analysis request
- Show you what to expect for notifications

## Troubleshooting

### No Notifications Received

1. **Check webhook URL**: Ensure `SLACK_WEBHOOK_URL` is correctly set
2. **Verify channel permissions**: Make sure the webhook has permission to post to your channel
3. **Check server logs**: Look for any error messages in the API server logs
4. **Test webhook manually**: Use curl to test your webhook URL directly

### Webhook Testing

```bash
curl -X POST $SLACK_WEBHOOK_URL \
     -H 'Content-Type: application/json' \
     -d '{"text": "Test message from RainbowPlus"}'
```

### Common Issues

- **Webhook URL expired**: Regenerate the webhook URL in Slack settings
- **Channel archived**: Ensure the target channel is active
- **App permissions**: Check if the Slack app has necessary permissions

## Security Notes

- Keep your webhook URL secure and don't commit it to version control
- Consider using environment variable files (`.env`) for local development
- Webhook URLs should be treated as sensitive credentials

## Disabling Notifications

To disable Slack notifications, simply:
1. Remove the `SLACK_WEBHOOK_URL` environment variable, or
2. Don't include the `nickname` parameter in your API requests

The system will continue to work normally without sending notifications.
