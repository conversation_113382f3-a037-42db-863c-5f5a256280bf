# Data Preparation Tool for DeepAssure

## Overview

This project provides a flexible data preparation pipeline for generating synthetic training data using Large Language Models (LLMs). It's designed to create diverse, high-quality sample documents based on descriptions, examples, and reference materials.

## Features

- **Configurable Pipeline**: Use YAML configuration files to define data generation tasks
- **LLM Integration**: Currently supports OpenAI models with a flexible provider architecture
- **Multiple Output Types**: Generate content samples or question-answer pairs
- **Command-line Interface**: Easy to use CLI for running the pipeline

## Project Structure

```
data-preparation/
├── core/                  # Core functionality
│   ├── config/            # Configuration handling
│   ├── io/                # Input/output data structures
│   ├── pipeline.py        # Main pipeline implementation
│   ├── prompt/            # Prompt templates
│   ├── provider/          # LLM provider implementations
│   └── utils.py           # Utility functions
├── recipes/               # Task recipes
│   ├── descriptions/      # Task descriptions
│   ├── documents/         # Reference documents
│   ├── examples/          # Example data
│   └── base.yml           # Base configuration
└── outputs/               # Generated outputs
```

## Installation

1. Clone the repository:
   ```bash
   git clone https://gitlab.knizsoft.com/delivery/deepassure/data-preparation.git
   cd data-preparation
   ```

2. Install dependencies:
   ```bash
   pip install -e .
   ```

## Usage

### Basic Usage

Run the pipeline with the default configuration:

```bash
python -m core.pipeline
```

This will generate samples based on the configuration in `./recipes/base.yml` and save the output to `./outputs/samples.json`.

### Custom Configuration

Specify a custom configuration file:

```bash
python -m core.pipeline --config ./path/to/config.yml
```

Specify a custom output location:

```bash
python -m core.pipeline --output ./path/to/output.json
```

### Configuration Format

The configuration file uses YAML format:

```yaml
description: ./path/to/description.txt  # Task description file

examples:                               # Example files (optional)
  - ./path/to/example1.txt
  - ./path/to/example2.txt

documents:                              # Reference documents (optional)
  - ./path/to/document1.txt

task: Content                           # Task type: Content or Question-Answering

llm:                                    # LLM configuration
  provider: openai                      # LLM provider
  api_key: your_api_key                 # API key
  model_kwargs:                         # Model parameters
    model: gpt-4o-mini
  sampling_params:                      # Sampling parameters
    temperature: 0.7
    max_tokens: 2048
    top_p: 0.9
```

## Output Format

The generated output is saved as a JSON file with the following structure for Content tasks:

```json
{
  "samples": [
    {
      "type": "content",
      "content": "Sample content text...",
      "label": "Label"
    },
    ...
  ]
}
```

And for Question-Answering tasks:

```json
{
  "samples": [
    {
      "type": "qa",
      "question": "Sample question?",
      "answer": "Sample answer."
    },
    ...
  ]
}
```

## Development

### Adding New Providers

To add a new LLM provider, create a new class in the `core/provider` directory that implements the required interface.

### Adding New Output Types

Output types are defined in `core/io/base.py`. Add new output types by creating new Pydantic models.

## License

Proprietary - DeepAssure
