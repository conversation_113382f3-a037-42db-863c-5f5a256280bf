from pydantic import BaseModel, Field
from typing import List
from typing_extensions import Literal


class Input(BaseModel):
    instruction: str
    examples: List[str]
    documents: List[str]

# Create enum class for type_
Type = Literal["qa", "content"]

class SampleOutput(BaseModel):
    type : Type

class QASampleOutput(SampleOutput):
    question: str = Field(..., description="Question of the sample")
    answer: str = Field(..., description="Answer of the sample")

class ContentSampleOutput(SampleOutput):
    content: str = Field(..., description="Only main content of the sample")
    label: str = Field(..., description="Label of the sample")
    
class QAOutput(BaseModel):
    samples: List[QASampleOutput]
    
class ContentOutput(BaseModel):
    samples: List[ContentSampleOutput]
    