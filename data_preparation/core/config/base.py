import yaml

from typing import List
from dataclasses import dataclass, field
from typing import Dict, Any
from yaml.parser import ParserError


@dataclass
class LLMConfig:
    """Configuration for a Language Model."""

    provider: str
    api_key: str = None
    base_url: str = None
    model_kwargs: Dict[str, Any] = field(default_factory=dict)
    sampling_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Config:
    """Configuration for a data preparation task."""
    description: str
    examples: List[str]
    documents: List[str]
    task: str
    llm: LLMConfig


class ConfigLoader:
    @staticmethod
    def load(file_path: str) -> Config:
        """Load configuration from YAML file."""
        try:
            with open(file_path, encoding="utf-8") as file:
                data = yaml.safe_load(file) or {}
                config = ConfigLoader._create_config(data)
                setattr(config, '_file_path', file_path)
                return config
        except ParserError as e:
            raise ValueError(f"Invalid YAML format: {e}")
        except OSError as e:
            raise ValueError(f"Failed to read config file: {e}")

    @staticmethod
    def _create_config(data: Dict[str, Any]) -> Config:
        """Create Configuration instance from parsed YAML data."""
        try:
            llm_config = ConfigLoader._parse_llm_config(data.get("llm", {}))

            return Config(
                description=data.get("description", ""),
                examples=data.get("examples", []),
                documents=data.get("documents", []),
                task=data.get("task", ""),
                llm=llm_config,
            )
        except KeyError as e:
            raise ValueError(f"Missing required configuration: {e}")

    @staticmethod
    def _parse_llm_config(data: Any) -> LLMConfig:
        """Parse LLM configuration from YAML data."""
        if not isinstance(data, dict):
            return None
        return LLMConfig(**data)       

        

# if __name__ == "__main__":
#     config = ConfigLoader.load("./recipes/base.yml")
#     print(config)
