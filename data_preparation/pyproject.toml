[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "deepassure"
version = "0.1.0"
description = "Data preparation tools for DeepAssure"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "Proprietary"}
authors = [
    {name = "DeepAssure Team"}
]
dependencies = [
    "openai",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[tool.setuptools]
packages = ["core"]

[tool.black]
line-length = 88
target-version = ["py38"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest]
testpaths = ["tests"]
