# RainbowPlus Backend Configuration Guide

This guide explains how to configure the RainbowPlus frontend to connect to different backend server IP addresses and environments.

## 🚀 Quick Start

### Method 1: Using the Shell Script (Recommended)

```bash
cd client

# Configure for local development (same machine)
./configure-backend.sh local

# Configure for a specific IP address
./configure-backend.sh http://*************:8000

# Configure for staging environment
./configure-backend.sh staging

# Check current configuration
./configure-backend.sh --current
```

### Method 2: Manual Configuration

```bash
cd client

# Edit the environment file directly
echo "NEXT_PUBLIC_API_URL=http://*************:8000" > .env.local

# Restart the development server
./run-dev.sh
```

## 📋 Available Configuration Methods

### 1. Shell Script (Works everywhere)
- **File**: `client/configure-backend.sh`
- **Requires**: Bash (available on Linux/macOS/WSL)
- **Usage**: `./configure-backend.sh [url-or-preset]`

### 2. Node.js Script (When Node.js is available)
- **File**: `client/scripts/configure-backend.js`
- **Requires**: Node.js
- **Usage**: `node scripts/configure-backend.js [url-or-preset]`

### 3. Manual Environment File Editing
- **File**: `client/.env.local`
- **Method**: Direct file editing

## 🎯 Preset Configurations

| Preset | URL | Description |
|--------|-----|-------------|
| `local` | `http://localhost:8000` | Backend on same machine |
| `local-ip` | `http://*************:8000` | Backend on local network |
| `staging` | `https://staging-api.rainbowplus.com` | Staging environment |
| `production` | `https://api.rainbowplus.com` | Production environment |

## 🌐 Common Scenarios

### Scenario 1: Backend on Same Machine (Default)
```bash
./configure-backend.sh local
```

### Scenario 2: Backend on Different Machine (Local Network)
```bash
# Replace with your backend server's IP
./configure-backend.sh http://*************:8000
```

### Scenario 3: Backend on Remote Server
```bash
./configure-backend.sh http://your-server.example.com:8000
```

### Scenario 4: Production Deployment
```bash
./configure-backend.sh production
```

## 🔧 Technical Details

### Configuration Files

1. **`.env.local`** - Local environment configuration (not committed to git)
2. **`.env.example`** - Template with all available options
3. **`lib/config.ts`** - Centralized configuration management
4. **`lib/api-client.ts`** - API client using the configuration
5. **`next.config.mjs`** - Next.js proxy configuration

### How It Works

1. **Environment Variables**: Configuration is stored in `.env.local`
2. **API Proxy**: In development, Next.js proxies `/api/*` to the backend
3. **Direct Calls**: In production, frontend makes direct API calls
4. **Centralized Config**: All components use the same configuration source

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_URL` | Backend API base URL | `http://localhost:8000` |
| `NEXT_PUBLIC_API_TIMEOUT` | Request timeout (ms) | `30000` |
| `PORT` | Frontend server port | `3001` |
| `NODE_ENV` | Environment mode | `development` |

## 🧪 Testing Configuration

Run the configuration test to verify everything is set up correctly:

```bash
cd client
./test-config.sh
```

This will check:
- ✅ Environment file exists and is valid
- ✅ All configuration files are present
- ✅ Next.js is properly configured
- ✅ Current backend URL setting

## 🔄 Changing Configuration

### Step-by-Step Process

1. **Stop the development server** (if running)
2. **Change the configuration** using one of the methods above
3. **Restart the development server**:
   ```bash
   ./run-dev.sh
   ```

### Verification

After changing configuration:

```bash
# Check current setting
./configure-backend.sh --current

# Test the connection (if backend is running)
curl http://your-backend-ip:8000/health
```

## 🚨 Troubleshooting

### Common Issues

1. **"Connection refused" errors**
   - ✅ Verify backend server is running
   - ✅ Check IP address and port
   - ✅ Verify firewall settings

2. **CORS errors**
   - ✅ In development: Check Next.js proxy configuration
   - ✅ In production: Configure CORS on backend

3. **Configuration not taking effect**
   - ✅ Restart the development server
   - ✅ Check `.env.local` file exists and has correct format
   - ✅ Clear browser cache

### Debug Steps

```bash
# 1. Check current configuration
./configure-backend.sh --current

# 2. Test backend connectivity
curl http://your-backend-ip:8000/health

# 3. Check environment file
cat .env.local

# 4. Run configuration test
./test-config.sh
```

## 📁 File Structure

```
client/
├── .env.local                    # Local configuration (you edit this)
├── .env.example                  # Configuration template
├── configure-backend.sh          # Shell configuration script
├── test-config.sh               # Configuration test script
├── CONFIG.md                    # Detailed configuration docs
├── lib/
│   ├── config.ts               # Centralized configuration
│   └── api-client.ts           # API client
├── scripts/
│   └── configure-backend.js    # Node.js configuration script
└── next.config.mjs             # Next.js configuration
```

## 🎯 Examples

### Example 1: Local Development Team
```bash
# Developer A (backend on same machine)
./configure-backend.sh local

# Developer B (backend on different machine)
./configure-backend.sh http://************:8000
```

### Example 2: Staging Deployment
```bash
# Point to staging server
./configure-backend.sh staging

# Or custom staging server
./configure-backend.sh http://staging.mycompany.com:8000
```

### Example 3: Production Deployment
```bash
# Use production preset
./configure-backend.sh production

# Or custom production server
./configure-backend.sh https://api.mycompany.com
```

## 📞 Support

If you encounter issues:

1. **Check the configuration**: `./configure-backend.sh --current`
2. **Run the test**: `./test-config.sh`
3. **Verify backend connectivity**: `curl http://backend-ip:8000/health`
4. **Check browser console** for error messages
5. **Restart both frontend and backend** servers

---

**Note**: After changing the backend configuration, always restart the development server with `./run-dev.sh` for changes to take effect.
